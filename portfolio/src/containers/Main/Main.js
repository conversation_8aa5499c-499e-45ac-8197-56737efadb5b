import React, { useState, useEffect } from "react";
import Greeting from "../greeting/Greeting";
import Skills from "../skills/Skills";
import Education from "../education/Education";
import WorkExperience from "../workExperience/WorkExperience";
import Projects from "../projects/Projects";
import OpenSource from "../opensource/OpenSource";
import BigProjects from "../bigProjects/BigProjects";
import Contact from "../contact/Contact";
import { projectsSection, splashScreen } from "../../portfolio";

const Main = () => {
  const [isShowingSplashAnimation, setIsShowingSplashAnimation] = useState(true);

  useEffect(() => {
    if (splashScreen.enabled) {
      const splashTimer = setTimeout(
        () => setIsShowingSplashAnimation(false),
        splashScreen.duration
      );
      return () => {
        clearTimeout(splashTimer);
      };
    }
  }, []);

  return (
    <div>
      {isShowingSplashAnimation && splashScreen.enabled ? (
        <SplashScreen />
      ) : (
        <>
          <Greeting />
          <Skills />
          <Education />
          <WorkExperience />
          <Projects projectsSection={projectsSection} />
          <OpenSource />
          <BigProjects />
          <Contact />
        </>
      )}
    </div>
  );
};

export default Main; 