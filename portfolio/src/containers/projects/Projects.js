import React from "react";
import { Fade } from "react-reveal";
import "./Projects.css";

const Projects = ({ projectsSection }) => {
  if (!projectsSection || !projectsSection.display) return null;

  return (
    <Fade bottom duration={1000} distance="20px">
      <div className="main" id="projects">
        <div className="projects-header">
          <h1 className="projects-header-title">{projectsSection.title}</h1>
          <p className="projects-header-subtitle">{projectsSection.subtitle}</p>
        </div>
        <div className="projects-main-div">
          {projectsSection.projects && projectsSection.projects.map((project, index) => (
            <Fade bottom duration={1000} distance="20px" key={index}>
              <div className="project-card">
                <h2 className="project-title">
                  <a
                    href={project.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="project-link"
                  >
                    {project.name}
                  </a>
                </h2>
                <p className="project-description">{project.description}</p>
              </div>
            </Fade>
          ))}
        </div>
      </div>
    </Fade>
  );
};

export default Projects; 