{"v": "5.6.5", "fr": 60, "ip": 0, "op": 240, "w": 1600, "h": 1200, "nm": "6_Targeting", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 5, "ty": 4, "nm": "Bee Wing 1", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 0, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 2.527, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 5.055, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 7.584, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 10.111, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 12.639, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 15.166, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 17.695, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 20.223, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 22.75, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 25.277, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 27.805, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 30.334, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 32.861, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 35.389, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 37.916, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 40.445, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 42.973, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 45.5, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 48.027, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 50.555, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 53.084, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 55.611, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 58.139, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 60.666, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 63.195, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 65.723, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 68.25, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 70.777, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 73.305, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 75.834, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 78.361, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 80.889, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 83.416, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 85.945, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 88.473, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 91, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 93.527, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 96.055, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 98.584, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 101.111, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 103.639, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 106.166, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 108.695, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 111.223, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 113.75, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 116.277, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 118.805, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 121.334, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 123.861, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 126.389, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 128.916, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 131.445, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 133.973, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 136.5, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 139.027, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 141.555, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 144.084, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 146.611, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 149.139, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 151.666, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 154.195, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 156.723, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 159.25, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 161.777, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 164.305, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 166.834, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 169.361, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 171.889, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 174.416, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 176.945, "s": [23.551]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 179.473, "s": [-18.216]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 182, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 184.527, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 187.055, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 189.584, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 192.111, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 194.639, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 197.166, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 199.695, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 202.223, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 204.75, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 207.277, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 209.805, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 212.334, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 214.861, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 217.389, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 219.916, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 222.445, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 224.973, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 227.5, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 230.027, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 232.555, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 235.084, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 237.611, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 240.139, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 242.666, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 245.195, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 247.723, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 250.25, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 252.777, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 255.305, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 257.834, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 260.361, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 262.889, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 265.416, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 267.945, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 270.473, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 273, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 275.527, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 278.055, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 280.584, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 283.111, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 285.639, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 288.166, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 290.695, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 293.223, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 295.75, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 298.277, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 300.805, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 303.334, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 305.861, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 308.389, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 310.916, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 313.445, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 315.973, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 318.5, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 321.027, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 323.555, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 326.084, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 328.611, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 331.139, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 333.666, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 336.195, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 338.723, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 341.25, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 343.777, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 346.305, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 348.834, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 351.361, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 353.889, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 356.416, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 358.945, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 361.473, "s": [16.863]}, {"t": 364, "s": [-25.114]}], "ix": 10}, "p": {"a": 0, "k": [21.353, 63.58, 0], "ix": 2}, "a": {"a": 0, "k": [-0.477, 21.656, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[12.463, 0.648], [0, 0], [-12.463, -0.648], [-0.648, 12.463]], "o": [[-12.394, -0.498], [0, 0], [12.394, 0.498], [0.649, -12.462]], "v": [[7.621, -22.534], [-29.772, -1.688], [5.56, 22.534], [29.124, 1.03]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.889999988032, 0.823999980852, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Pink').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [30.023, 23.282], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Bee Body", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 53.692, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [1009.309, 64.051, 0], "to": [1.691, 129.949, 0], "ti": [-39.691, 35.051, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [1162.309, 207.051, 0], "to": [39.691, -35.051, 0], "ti": [70.309, -59.949, 0]}, {"t": 240, "s": [1009.309, 64.051, 0]}], "ix": 2}, "a": {"a": 0, "k": [17.405, 54.523, 0], "ix": 1}, "s": {"a": 0, "k": [85.9, 85.9, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.38, 0.038], [-0.038, -1.381]], "o": [[-0.038, -1.38], [1.38, -0.038], [0, 0]], "v": [[-2.689, -6.79], [-0.259, -9.358], [2.309, -6.927]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [1.381, -0.038], [0.038, 1.38]], "o": [[0.039, 1.379], [-1.38, 0.039], [0, 0]], "v": [[2.689, 6.789], [0.259, 9.357], [-2.31, 6.927]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.309, -6.927], [2.689, 6.789], [-2.31, 6.927], [-2.689, -6.79]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [20.945, 99.4], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-14.837, -9.318], [14.794, -10.63], [14.837, 9.687], [-14.782, 10.63]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [19.722, 60.463], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.372, 0.158], [-0.158, 1.371]], "o": [[-0.158, 1.372], [-1.372, -0.157], [0, 0]], "v": [[0.723, 15.573], [-2.048, 17.77], [-4.245, 15.001]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.372, -0.158], [0.159, -1.371]], "o": [[0.158, -1.372], [1.372, 0.157], [0, 0]], "v": [[-0.724, -15.574], [2.046, -17.771], [4.243, -15.001]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.245, 15.001], [-0.724, -15.574], [4.243, -15.001], [0.723, 15.573]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.066, 18.179], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 5, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.167, 0.738], [-0.738, -1.167]], "o": [[-0.738, -1.167], [1.167, -0.737], [0, 0]], "v": [[-10.032, -11.2], [-9.254, -14.649], [-5.805, -13.869]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [1.167, -0.737], [0.738, 1.167]], "o": [[0.738, 1.168], [-1.167, 0.738], [0, 0]], "v": [[10.032, 11.198], [9.254, 14.647], [5.805, 13.869]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.805, -13.869], [10.032, 11.198], [5.805, 13.869], [-10.032, -11.2]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [11.02, 21.916], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 5, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-8.129, 0.091], [0.091, 8.13], [0, 0], [8.129, -0.091], [-0.091, -8.129], [0, 0]], "o": [[8.239, -0.05], [0, 0], [-0.05, -8.239], [-8.239, 0.051], [0, 0], [0.091, 8.13]], "v": [[0.067, 33.652], [14.795, 18.828], [14.758, -18.924], [-0.067, -33.653], [-14.796, -18.828], [-14.758, 18.924]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [19.673, 62.622], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Bee Wing 2", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 0, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 2.527, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 5.055, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 7.584, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 10.111, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 12.639, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 15.166, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 17.695, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 20.223, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 22.75, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 25.277, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 27.805, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 30.334, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 32.861, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 35.389, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 37.916, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 40.445, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 42.973, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 45.5, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 48.027, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 50.555, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 53.084, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 55.611, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 58.139, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 60.666, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 63.195, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 65.723, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 68.25, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 70.777, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 73.305, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 75.834, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 78.361, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 80.889, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 83.416, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 85.945, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 88.473, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 91, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 93.527, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 96.055, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 98.584, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 101.111, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 103.639, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 106.166, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 108.695, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 111.223, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 113.75, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 116.277, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 118.805, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 121.334, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 123.861, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 126.389, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 128.916, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 131.445, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 133.973, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 136.5, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 139.027, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 141.555, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 144.084, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 146.611, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 149.139, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 151.666, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 154.195, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 156.723, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 159.25, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 161.777, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 164.305, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 166.834, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 169.361, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 171.889, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 174.416, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 176.945, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 179.473, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 182, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 184.527, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 187.055, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 189.584, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 192.111, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 194.639, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 197.166, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 199.695, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 202.223, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 204.75, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 207.277, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 209.805, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 212.334, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 214.861, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 217.389, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 219.916, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 222.445, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 224.973, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 227.5, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 230.027, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 232.555, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 235.084, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 237.611, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 240.139, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 242.666, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 245.195, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 247.723, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 250.25, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 252.777, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 255.305, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 257.834, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 260.361, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 262.889, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 265.416, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 267.945, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 270.473, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 273, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 275.527, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 278.055, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 280.584, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 283.111, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 285.639, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 288.166, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 290.695, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 293.223, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 295.75, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 298.277, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 300.805, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 303.334, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 305.861, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 308.389, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 310.916, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 313.445, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 315.973, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 318.5, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 321.027, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 323.555, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 326.084, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 328.611, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 331.139, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 333.666, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 336.195, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 338.723, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 341.25, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 343.777, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 346.305, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 348.834, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 351.361, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 353.889, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 356.416, "s": [16.863]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 358.945, "s": [-25.114]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 361.473, "s": [16.863]}, {"t": 364, "s": [-25.114]}], "ix": 10}, "p": {"a": 0, "k": [16.683, 63.318, 0], "ix": 2}, "a": {"a": 0, "k": [59.342, 24.907, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-12.394, -0.498], [0, 0], [12.463, 0.649], [0.648, -12.463]], "o": [[12.394, 0.499], [0, 0], [-12.394, -0.498], [-0.539, 12.503]], "v": [[-7.675, 22.533], [29.717, 1.688], [-5.616, -22.534], [-29.178, -1.03]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.889999988032, 0.823999980852, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Pink').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [29.967, 23.282], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Arrow", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 30, "s": [0]}, {"i": {"x": [0.794], "y": [0.931]}, "o": {"x": [0.382], "y": [0]}, "t": 90, "s": [13.039]}, {"t": 92, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = $bm_div(effect('Rotation - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = $bm_div(effect('Rotation - Bounce')('ADBE Slider Control-0001'), 20), decay = $bm_div(effect('Rotation - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "p": {"a": 0, "k": [481.25, 587.75, 0], "ix": 2}, "a": {"a": 0, "k": [-318.75, -12.25, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Rotation - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - <PERSON><PERSON><PERSON>", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Rotation - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-319, -12.5], [-194.5, -137]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.03137254902, 0, 0.223529426724, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-11.451, 50.398], [-43.907, 43.907], [-50.398, 11.329], [11.451, -50.398], [17.943, -17.942], [50.398, -11.451]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-176.564, -154.852], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Head", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 11, "s": [0]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 34, "s": [-16.344]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 108, "s": [-16.344]}, {"t": 122, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 11, "s": [952.061, 255.901, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.5, "y": 0.5}, "o": {"x": 0.5, "y": 0.5}, "t": 34, "s": [928.061, 255.901, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 108, "s": [928.061, 255.901, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 122, "s": [952.061, 255.901, 0]}], "ix": 2}, "a": {"a": 0, "k": [38.75, 22.35, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.209, 0], [0, 2.209], [2.209, 0], [0, -2.209]], "o": [[2.209, 0], [0, -2.209], [-2.209, 0], [0, 2.209]], "v": [[0, 4], [4, 0], [0, -4], [-4, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [57.051, 18.449], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.209, 0], [0, 2.209], [2.209, 0], [0, -2.209]], "o": [[2.209, 0], [0, -2.209], [-2.209, 0], [0, 2.209]], "v": [[0, 4], [4, 0], [0, -4], [-4, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.051, 18.449], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.38, -0.032], [-0.031, -1.381]], "o": [[-0.031, -1.38], [-1.381, 0.031], [0, 0]], "v": [[10.434, -4.157], [7.878, -6.599], [5.435, -4.043]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.019, -0.001]], "o": [[0, 0], [-0.019, 0], [0, 0]], "v": [[0.034, 4], [0.034, 1.5], [-0.023, 1.501]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [1.381, 0], [0, -1.381]], "o": [[0, -1.381], [-1.381, 0], [0, 0]], "v": [[-5.565, -3.9], [-8.065, -6.4], [-10.565, -3.9]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [2.99, 0], [0, 0], [0.132, 5.795]], "o": [[0.068, 3.004], [0, 0], [5.81, 0], [0, 0]], "v": [[5.435, -4.043], [0.034, 1.5], [0.034, 6.5], [10.434, -4.157]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 2.99], [0, 0], [-5.796, 0.132]], "o": [[-3.005, 0.068], [0, 0], [0, 5.81], [0, 0]], "v": [[-0.023, 1.501], [-5.565, -3.9], [-10.565, -3.9], [0.091, 6.499]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.816, 29.95], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 7, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.2, -11.9], [12, 0.2], [0, 0], [-0.2, 12], [-12, -0.2], [0, 0]], "o": [[-0.199, 12], [0, 0], [-11.899, -0.2], [0.201, -11.9], [0, 0], [12, 0.2]], "v": [[38.3, 0.6], [16.3, 21.9], [-17, 21.4], [-38.3, -0.6], [-16.3, -21.9], [17, -21.4]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [38.75, 22.35], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Hair 1", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [93.751, -7.851, 0], "to": [-0.167, 0, 0], "ti": [0.334, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [92.75, -7.851, 0], "to": [-0.334, 0, 0], "ti": [0.334, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [91.744, -7.851, 0], "to": [-0.334, 0, 0], "ti": [0.332, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [90.748, -7.851, 0], "to": [-0.332, 0, 0], "ti": [0.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [89.754, -7.851, 0], "to": [-0.333, 0, 0], "ti": [0.334, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [88.751, -7.851, 0], "to": [-0.334, 0, 0], "ti": [0.334, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [87.751, -7.851, 0], "to": [-0.334, 0, 0], "ti": [0.316, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [86.748, -7.851, 0], "to": [-0.316, 0, 0], "ti": [0.275, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [85.853, -7.851, 0], "to": [-0.275, 0, 0], "ti": [0.224, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [85.098, -7.851, 0], "to": [-0.224, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [84.506, -7.851, 0], "to": [-0.167, 0, 0], "ti": [0.107, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [84.094, -7.851, 0], "to": [-0.107, 0, 0], "ti": [0.045, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [83.866, -7.851, 0], "to": [-0.045, 0, 0], "ti": [-0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [83.822, -7.851, 0], "to": [0.014, 0, 0], "ti": [-0.069, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [83.951, -7.851, 0], "to": [0.069, 0, 0], "ti": [-0.117, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [84.236, -7.851, 0], "to": [0.117, 0, 0], "ti": [-0.158, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [84.655, -7.851, 0], "to": [0.158, 0, 0], "ti": [-0.188, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [85.182, -7.851, 0], "to": [0.188, 0, 0], "ti": [-0.209, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [85.785, -7.851, 0], "to": [0.209, 0, 0], "ti": [-0.219, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [86.436, -7.851, 0], "to": [0.219, 0, 0], "ti": [-0.219, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [87.101, -7.851, 0], "to": [0.219, 0, 0], "ti": [-0.21, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47, "s": [87.751, -7.851, 0], "to": [0.21, 0, 0], "ti": [-0.192, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [88.36, -7.851, 0], "to": [0.192, 0, 0], "ti": [-0.167, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [88.903, -7.851, 0], "to": [0.167, 0, 0], "ti": [-0.136, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [89.361, -7.851, 0], "to": [0.136, 0, 0], "ti": [-0.101, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51, "s": [89.719, -7.851, 0], "to": [0.101, 0, 0], "ti": [-0.065, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [89.97, -7.851, 0], "to": [0.065, 0, 0], "ti": [-0.027, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [90.108, -7.851, 0], "to": [0.027, 0, 0], "ti": [0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [90.134, -7.851, 0], "to": [-0.009, 0, 0], "ti": [0.042, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [90.056, -7.851, 0], "to": [-0.042, 0, 0], "ti": [0.071, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [89.883, -7.851, 0], "to": [-0.071, 0, 0], "ti": [0.096, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [89.629, -7.851, 0], "to": [-0.096, 0, 0], "ti": [0.114, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [89.31, -7.851, 0], "to": [-0.114, 0, 0], "ti": [0.127, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59, "s": [88.944, -7.851, 0], "to": [-0.127, 0, 0], "ti": [0.133, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [88.549, -7.851, 0], "to": [-0.133, 0, 0], "ti": [0.133, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [88.146, -7.851, 0], "to": [-0.133, 0, 0], "ti": [0.127, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [87.751, -7.851, 0], "to": [-0.127, 0, 0], "ti": [0.116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [87.382, -7.851, 0], "to": [-0.116, 0, 0], "ti": [0.101, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [87.053, -7.851, 0], "to": [-0.101, 0, 0], "ti": [0.083, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [86.775, -7.851, 0], "to": [-0.083, 0, 0], "ti": [0.062, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [86.557, -7.851, 0], "to": [-0.062, 0, 0], "ti": [0.039, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [86.406, -7.851, 0], "to": [-0.039, 0, 0], "ti": [0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [86.322, -7.851, 0], "to": [-0.017, 0, 0], "ti": [-0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [86.306, -7.851, 0], "to": [0.005, 0, 0], "ti": [-0.025, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [86.353, -7.851, 0], "to": [0.025, 0, 0], "ti": [-0.043, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [86.458, -7.851, 0], "to": [0.043, 0, 0], "ti": [-0.058, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [86.612, -7.851, 0], "to": [0.058, 0, 0], "ti": [-0.069, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [86.806, -7.851, 0], "to": [0.069, 0, 0], "ti": [-0.077, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [87.028, -7.851, 0], "to": [0.077, 0, 0], "ti": [-0.081, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [87.267, -7.851, 0], "to": [0.081, 0, 0], "ti": [-0.081, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [87.512, -7.851, 0], "to": [0.081, 0, 0], "ti": [-0.077, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [87.751, -7.851, 0], "to": [0.077, 0, 0], "ti": [-0.071, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [87.975, -7.851, 0], "to": [0.071, 0, 0], "ti": [-0.061, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [88.175, -7.851, 0], "to": [0.061, 0, 0], "ti": [-0.05, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [88.343, -7.851, 0], "to": [0.05, 0, 0], "ti": [-0.037, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [88.475, -7.851, 0], "to": [0.037, 0, 0], "ti": [-0.024, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [88.567, -7.851, 0], "to": [0.024, 0, 0], "ti": [-0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [88.618, -7.851, 0], "to": [0.01, 0, 0], "ti": [0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [88.628, -7.851, 0], "to": [-0.003, 0, 0], "ti": [0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [88.599, -7.851, 0], "to": [-0.015, 0, 0], "ti": [0.026, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [88.536, -7.851, 0], "to": [-0.026, 0, 0], "ti": [0.035, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [88.442, -7.851, 0], "to": [-0.035, 0, 0], "ti": [0.042, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [88.325, -7.851, 0], "to": [-0.042, 0, 0], "ti": [0.047, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [88.19, -7.851, 0], "to": [-0.047, 0, 0], "ti": [0.049, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [88.045, -7.851, 0], "to": [-0.049, 0, 0], "ti": [0.049, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [87.896, -7.851, 0], "to": [-0.049, 0, 0], "ti": [0.047, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [87.751, -7.851, 0], "to": [-0.047, 0, 0], "ti": [0.043, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [87.615, -7.851, 0], "to": [-0.043, 0, 0], "ti": [0.037, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [87.494, -7.851, 0], "to": [-0.037, 0, 0], "ti": [0.03, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [87.392, -7.851, 0], "to": [-0.03, 0, 0], "ti": [0.023, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [87.312, -7.851, 0], "to": [-0.023, 0, 0], "ti": [0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [87.256, -7.851, 0], "to": [-0.014, 0, 0], "ti": [0.006, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [87.225, -7.851, 0], "to": [-0.006, 0, 0], "ti": [-0.002, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [87.219, -7.851, 0], "to": [0.002, 0, 0], "ti": [-0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [87.237, -7.851, 0], "to": [0.009, 0, 0], "ti": [-0.016, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [87.275, -7.851, 0], "to": [0.016, 0, 0], "ti": [-0.021, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [87.332, -7.851, 0], "to": [0.021, 0, 0], "ti": [-0.025, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [87.403, -7.851, 0], "to": [0.025, 0, 0], "ti": [-0.028, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [87.485, -7.851, 0], "to": [0.028, 0, 0], "ti": [-0.03, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [87.573, -7.851, 0], "to": [0.03, 0, 0], "ti": [-0.03, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [87.663, -7.851, 0], "to": [0.03, 0, 0], "ti": [-0.028, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [87.751, -7.851, 0], "to": [0.028, 0, 0], "ti": [-0.026, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [87.834, -7.851, 0], "to": [0.026, 0, 0], "ti": [-0.023, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [87.907, -7.851, 0], "to": [0.023, 0, 0], "ti": [-0.018, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [87.969, -7.851, 0], "to": [0.018, 0, 0], "ti": [-0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111, "s": [88.018, -7.851, 0], "to": [0.014, 0, 0], "ti": [-0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [88.051, -7.851, 0], "to": [0.009, 0, 0], "ti": [-0.004, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [88.07, -7.851, 0], "to": [0.004, 0, 0], "ti": [0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [88.074, -7.851, 0], "to": [-0.001, 0, 0], "ti": [0.054, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [88.063, -7.851, 0], "to": [-0.054, 0, 0], "ti": [-0.115, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [87.751, -7.851, 0], "to": [0.115, 0, 0], "ti": [-0.334, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117, "s": [88.751, -7.851, 0], "to": [0.334, 0, 0], "ti": [-0.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [89.754, -7.851, 0], "to": [0.333, 0, 0], "ti": [-0.332, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [90.748, -7.851, 0], "to": [0.332, 0, 0], "ti": [-0.334, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [91.744, -7.851, 0], "to": [0.334, 0, 0], "ti": [-0.334, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121, "s": [92.75, -7.851, 0], "to": [0.334, 0, 0], "ti": [-0.336, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [93.751, -7.851, 0], "to": [0.336, 0, 0], "ti": [-0.32, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [94.766, -7.851, 0], "to": [0.32, 0, 0], "ti": [-0.278, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [95.672, -7.851, 0], "to": [0.278, 0, 0], "ti": [-0.227, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [96.436, -7.851, 0], "to": [0.227, 0, 0], "ti": [-0.169, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126, "s": [97.035, -7.851, 0], "to": [0.169, 0, 0], "ti": [-0.108, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127, "s": [97.452, -7.851, 0], "to": [0.108, 0, 0], "ti": [-0.046, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128, "s": [97.682, -7.851, 0], "to": [0.046, 0, 0], "ti": [0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 129, "s": [97.727, -7.851, 0], "to": [-0.014, 0, 0], "ti": [0.07, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [97.597, -7.851, 0], "to": [-0.07, 0, 0], "ti": [0.119, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [97.308, -7.851, 0], "to": [-0.119, 0, 0], "ti": [0.159, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [96.884, -7.851, 0], "to": [-0.159, 0, 0], "ti": [0.191, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133, "s": [96.351, -7.851, 0], "to": [-0.191, 0, 0], "ti": [0.211, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [95.74, -7.851, 0], "to": [-0.211, 0, 0], "ti": [0.222, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [95.083, -7.851, 0], "to": [-0.222, 0, 0], "ti": [0.222, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136, "s": [94.409, -7.851, 0], "to": [-0.222, 0, 0], "ti": [0.212, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [93.751, -7.851, 0], "to": [-0.212, 0, 0], "ti": [0.194, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138, "s": [93.135, -7.851, 0], "to": [-0.194, 0, 0], "ti": [0.169, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [92.586, -7.851, 0], "to": [-0.169, 0, 0], "ti": [0.138, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [92.123, -7.851, 0], "to": [-0.138, 0, 0], "ti": [0.103, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [91.76, -7.851, 0], "to": [-0.103, 0, 0], "ti": [0.065, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [91.507, -7.851, 0], "to": [-0.065, 0, 0], "ti": [0.028, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143, "s": [91.367, -7.851, 0], "to": [-0.028, 0, 0], "ti": [-0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [91.34, -7.851, 0], "to": [0.009, 0, 0], "ti": [-0.042, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [91.419, -7.851, 0], "to": [0.042, 0, 0], "ti": [-0.072, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [91.594, -7.851, 0], "to": [0.072, 0, 0], "ti": [-0.097, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [91.851, -7.851, 0], "to": [0.097, 0, 0], "ti": [-0.116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148, "s": [92.174, -7.851, 0], "to": [0.116, 0, 0], "ti": [-0.128, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [92.545, -7.851, 0], "to": [0.128, 0, 0], "ti": [-0.135, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [92.944, -7.851, 0], "to": [0.135, 0, 0], "ti": [-0.135, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [93.352, -7.851, 0], "to": [0.135, 0, 0], "ti": [-0.129, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [93.751, -7.851, 0], "to": [0.129, 0, 0], "ti": [-0.118, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153, "s": [94.125, -7.851, 0], "to": [0.118, 0, 0], "ti": [-0.102, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [94.458, -7.851, 0], "to": [0.102, 0, 0], "ti": [-0.084, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [94.739, -7.851, 0], "to": [0.084, 0, 0], "ti": [-0.062, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156, "s": [94.959, -7.851, 0], "to": [0.062, 0, 0], "ti": [-0.04, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157, "s": [95.113, -7.851, 0], "to": [0.04, 0, 0], "ti": [-0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [95.197, -7.851, 0], "to": [0.017, 0, 0], "ti": [0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159, "s": [95.214, -7.851, 0], "to": [-0.005, 0, 0], "ti": [0.026, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [95.166, -7.851, 0], "to": [-0.026, 0, 0], "ti": [0.044, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [95.06, -7.851, 0], "to": [-0.044, 0, 0], "ti": [0.059, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162, "s": [94.904, -7.851, 0], "to": [-0.059, 0, 0], "ti": [0.07, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [94.708, -7.851, 0], "to": [-0.07, 0, 0], "ti": [0.078, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [94.483, -7.851, 0], "to": [-0.078, 0, 0], "ti": [0.082, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [94.241, -7.851, 0], "to": [-0.082, 0, 0], "ti": [0.082, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166, "s": [93.993, -7.851, 0], "to": [-0.082, 0, 0], "ti": [0.078, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [93.751, -7.851, 0], "to": [-0.078, 0, 0], "ti": [0.071, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168, "s": [93.525, -7.851, 0], "to": [-0.071, 0, 0], "ti": [0.062, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169, "s": [93.323, -7.851, 0], "to": [-0.062, 0, 0], "ti": [0.051, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [93.152, -7.851, 0], "to": [-0.051, 0, 0], "ti": [0.038, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171, "s": [93.019, -7.851, 0], "to": [-0.038, 0, 0], "ti": [0.024, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172, "s": [92.925, -7.851, 0], "to": [-0.024, 0, 0], "ti": [0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [92.874, -7.851, 0], "to": [-0.01, 0, 0], "ti": [-0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [92.864, -7.851, 0], "to": [0.003, 0, 0], "ti": [-0.016, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [92.893, -7.851, 0], "to": [0.016, 0, 0], "ti": [-0.027, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176, "s": [92.958, -7.851, 0], "to": [0.027, 0, 0], "ti": [-0.036, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177, "s": [93.052, -7.851, 0], "to": [0.036, 0, 0], "ti": [-0.043, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178, "s": [93.171, -7.851, 0], "to": [0.043, 0, 0], "ti": [-0.047, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [93.307, -7.851, 0], "to": [0.047, 0, 0], "ti": [-0.049, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [93.454, -7.851, 0], "to": [0.049, 0, 0], "ti": [-0.05, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181, "s": [93.604, -7.851, 0], "to": [0.05, 0, 0], "ti": [-0.047, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182, "s": [93.751, -7.851, 0], "to": [0.047, 0, 0], "ti": [-0.043, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183, "s": [93.889, -7.851, 0], "to": [0.043, 0, 0], "ti": [-0.038, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 184, "s": [94.011, -7.851, 0], "to": [0.038, 0, 0], "ti": [-0.031, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [94.115, -7.851, 0], "to": [0.031, 0, 0], "ti": [-0.023, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 186, "s": [94.196, -7.851, 0], "to": [0.023, 0, 0], "ti": [-0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187, "s": [94.252, -7.851, 0], "to": [0.015, 0, 0], "ti": [-0.006, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188, "s": [94.283, -7.851, 0], "to": [0.006, 0, 0], "ti": [0.002, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 189, "s": [94.289, -7.851, 0], "to": [-0.002, 0, 0], "ti": [0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [94.272, -7.851, 0], "to": [-0.009, 0, 0], "ti": [0.016, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191, "s": [94.233, -7.851, 0], "to": [-0.016, 0, 0], "ti": [0.022, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192, "s": [94.175, -7.851, 0], "to": [-0.022, 0, 0], "ti": [0.026, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193, "s": [94.103, -7.851, 0], "to": [-0.026, 0, 0], "ti": [0.029, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [94.02, -7.851, 0], "to": [-0.029, 0, 0], "ti": [0.03, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [93.931, -7.851, 0], "to": [-0.03, 0, 0], "ti": [0.03, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [93.84, -7.851, 0], "to": [-0.03, 0, 0], "ti": [0.029, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [93.751, -7.851, 0], "to": [-0.029, 0, 0], "ti": [0.026, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198, "s": [93.668, -7.851, 0], "to": [-0.026, 0, 0], "ti": [0.023, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199, "s": [93.594, -7.851, 0], "to": [-0.023, 0, 0], "ti": [0.019, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [93.531, -7.851, 0], "to": [-0.019, 0, 0], "ti": [0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 201, "s": [93.482, -7.851, 0], "to": [-0.014, 0, 0], "ti": [0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [93.447, -7.851, 0], "to": [-0.009, 0, 0], "ti": [0.004, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [93.429, -7.851, 0], "to": [-0.004, 0, 0], "ti": [-0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 204, "s": [93.425, -7.851, 0], "to": [0.001, 0, 0], "ti": [-0.006, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [93.436, -7.851, 0], "to": [0.006, 0, 0], "ti": [-0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [93.459, -7.851, 0], "to": [0.01, 0, 0], "ti": [-0.013, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [93.494, -7.851, 0], "to": [0.013, 0, 0], "ti": [-0.016, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208, "s": [93.538, -7.851, 0], "to": [0.016, 0, 0], "ti": [-0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 209, "s": [93.588, -7.851, 0], "to": [0.017, 0, 0], "ti": [-0.018, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [93.642, -7.851, 0], "to": [0.018, 0, 0], "ti": [-0.018, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [93.697, -7.851, 0], "to": [0.018, 0, 0], "ti": [-0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 212, "s": [93.751, -7.851, 0], "to": [0.017, 0, 0], "ti": [-0.016, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213, "s": [93.802, -7.851, 0], "to": [0.016, 0, 0], "ti": [-0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 214, "s": [93.847, -7.851, 0], "to": [0.014, 0, 0], "ti": [-0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [93.885, -7.851, 0], "to": [0.011, 0, 0], "ti": [-0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 216, "s": [93.915, -7.851, 0], "to": [0.008, 0, 0], "ti": [-0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217, "s": [93.935, -7.851, 0], "to": [0.005, 0, 0], "ti": [-0.002, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218, "s": [93.947, -7.851, 0], "to": [0.002, 0, 0], "ti": [0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [93.949, -7.851, 0], "to": [-0.001, 0, 0], "ti": [0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [93.943, -7.851, 0], "to": [-0.003, 0, 0], "ti": [0.006, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [93.928, -7.851, 0], "to": [-0.006, 0, 0], "ti": [0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222, "s": [93.907, -7.851, 0], "to": [-0.008, 0, 0], "ti": [0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223, "s": [93.881, -7.851, 0], "to": [-0.009, 0, 0], "ti": [0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [93.85, -7.851, 0], "to": [-0.011, 0, 0], "ti": [0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [93.817, -7.851, 0], "to": [-0.011, 0, 0], "ti": [0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [93.784, -7.851, 0], "to": [-0.011, 0, 0], "ti": [0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [93.751, -7.851, 0], "to": [-0.011, 0, 0], "ti": [0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228, "s": [93.721, -7.851, 0], "to": [-0.01, 0, 0], "ti": [0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229, "s": [93.693, -7.851, 0], "to": [-0.008, 0, 0], "ti": [0.007, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [93.67, -7.851, 0], "to": [-0.007, 0, 0], "ti": [0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231, "s": [93.652, -7.851, 0], "to": [-0.005, 0, 0], "ti": [0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232, "s": [93.639, -7.851, 0], "to": [-0.003, 0, 0], "ti": [0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233, "s": [93.632, -7.851, 0], "to": [-0.001, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234, "s": [93.631, -7.851, 0], "to": [0, 0, 0], "ti": [-0.002, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235, "s": [93.635, -7.851, 0], "to": [0.002, 0, 0], "ti": [-0.004, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 236, "s": [93.644, -7.851, 0], "to": [0.004, 0, 0], "ti": [-0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [93.657, -7.851, 0], "to": [0.005, 0, 0], "ti": [-0.006, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [93.673, -7.851, 0], "to": [0.006, 0, 0], "ti": [-0.003, 0, 0]}, {"t": 239, "s": [93.691, -7.851, 0]}], "ix": 2}, "a": {"a": 0, "k": [16.95, 16.95, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-9.224, 0], [0, 9.223], [9.223, 0], [0, -9.223]], "o": [[9.223, 0], [0, -9.223], [-9.224, 0], [0, 9.223]], "v": [[0, 16.7], [16.7, 0], [0, -16.7], [-16.7, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.95, 16.95], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Hair 2", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [88.551, -39.051, 0], "to": [-0.167, 0, 0], "ti": [0.334, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [87.551, -39.051, 0], "to": [-0.334, 0, 0], "ti": [0.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [86.548, -39.051, 0], "to": [-0.333, 0, 0], "ti": [0.332, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [85.554, -39.051, 0], "to": [-0.332, 0, 0], "ti": [0.334, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [84.558, -39.051, 0], "to": [-0.334, 0, 0], "ti": [0.334, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [83.552, -39.051, 0], "to": [-0.334, 0, 0], "ti": [0.336, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [82.551, -39.051, 0], "to": [-0.336, 0, 0], "ti": [0.32, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [81.536, -39.051, 0], "to": [-0.32, 0, 0], "ti": [0.278, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [80.63, -39.051, 0], "to": [-0.278, 0, 0], "ti": [0.227, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [79.866, -39.051, 0], "to": [-0.227, 0, 0], "ti": [0.169, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [79.268, -39.051, 0], "to": [-0.169, 0, 0], "ti": [0.108, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [78.85, -39.051, 0], "to": [-0.108, 0, 0], "ti": [0.046, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [78.62, -39.051, 0], "to": [-0.046, 0, 0], "ti": [-0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [78.575, -39.051, 0], "to": [0.014, 0, 0], "ti": [-0.07, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [78.705, -39.051, 0], "to": [0.07, 0, 0], "ti": [-0.119, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [78.994, -39.051, 0], "to": [0.119, 0, 0], "ti": [-0.159, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [79.418, -39.051, 0], "to": [0.159, 0, 0], "ti": [-0.191, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [79.951, -39.051, 0], "to": [0.191, 0, 0], "ti": [-0.211, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [80.562, -39.051, 0], "to": [0.211, 0, 0], "ti": [-0.222, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47, "s": [81.22, -39.051, 0], "to": [0.222, 0, 0], "ti": [-0.222, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [81.893, -39.051, 0], "to": [0.222, 0, 0], "ti": [-0.212, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [82.551, -39.051, 0], "to": [0.212, 0, 0], "ti": [-0.194, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [83.167, -39.051, 0], "to": [0.194, 0, 0], "ti": [-0.169, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51, "s": [83.716, -39.051, 0], "to": [0.169, 0, 0], "ti": [-0.138, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [84.18, -39.051, 0], "to": [0.138, 0, 0], "ti": [-0.103, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [84.543, -39.051, 0], "to": [0.103, 0, 0], "ti": [-0.065, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [84.796, -39.051, 0], "to": [0.065, 0, 0], "ti": [-0.028, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [84.935, -39.051, 0], "to": [0.028, 0, 0], "ti": [0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [84.963, -39.051, 0], "to": [-0.009, 0, 0], "ti": [0.042, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [84.883, -39.051, 0], "to": [-0.042, 0, 0], "ti": [0.072, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [84.708, -39.051, 0], "to": [-0.072, 0, 0], "ti": [0.097, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59, "s": [84.451, -39.051, 0], "to": [-0.097, 0, 0], "ti": [0.116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [84.128, -39.051, 0], "to": [-0.116, 0, 0], "ti": [0.128, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [83.757, -39.051, 0], "to": [-0.128, 0, 0], "ti": [0.135, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [83.358, -39.051, 0], "to": [-0.135, 0, 0], "ti": [0.135, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [82.95, -39.051, 0], "to": [-0.135, 0, 0], "ti": [0.129, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [82.551, -39.051, 0], "to": [-0.129, 0, 0], "ti": [0.118, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [82.178, -39.051, 0], "to": [-0.118, 0, 0], "ti": [0.102, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [81.844, -39.051, 0], "to": [-0.102, 0, 0], "ti": [0.084, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [81.563, -39.051, 0], "to": [-0.084, 0, 0], "ti": [0.062, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [81.343, -39.051, 0], "to": [-0.062, 0, 0], "ti": [0.04, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [81.19, -39.051, 0], "to": [-0.04, 0, 0], "ti": [0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [81.105, -39.051, 0], "to": [-0.017, 0, 0], "ti": [-0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [81.088, -39.051, 0], "to": [0.005, 0, 0], "ti": [-0.026, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [81.136, -39.051, 0], "to": [0.026, 0, 0], "ti": [-0.044, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [81.242, -39.051, 0], "to": [0.044, 0, 0], "ti": [-0.059, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [81.399, -39.051, 0], "to": [0.059, 0, 0], "ti": [-0.07, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [81.594, -39.051, 0], "to": [0.07, 0, 0], "ti": [-0.078, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [81.819, -39.051, 0], "to": [0.078, 0, 0], "ti": [-0.082, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [82.061, -39.051, 0], "to": [0.082, 0, 0], "ti": [-0.082, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [82.309, -39.051, 0], "to": [0.082, 0, 0], "ti": [-0.078, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [82.551, -39.051, 0], "to": [0.078, 0, 0], "ti": [-0.071, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [82.778, -39.051, 0], "to": [0.071, 0, 0], "ti": [-0.062, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [82.98, -39.051, 0], "to": [0.062, 0, 0], "ti": [-0.051, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [83.15, -39.051, 0], "to": [0.051, 0, 0], "ti": [-0.038, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [83.284, -39.051, 0], "to": [0.038, 0, 0], "ti": [-0.024, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [83.377, -39.051, 0], "to": [0.024, 0, 0], "ti": [-0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [83.428, -39.051, 0], "to": [0.01, 0, 0], "ti": [0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [83.438, -39.051, 0], "to": [-0.003, 0, 0], "ti": [0.016, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [83.409, -39.051, 0], "to": [-0.016, 0, 0], "ti": [0.027, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [83.345, -39.051, 0], "to": [-0.027, 0, 0], "ti": [0.036, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [83.25, -39.051, 0], "to": [-0.036, 0, 0], "ti": [0.043, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [83.131, -39.051, 0], "to": [-0.043, 0, 0], "ti": [0.047, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [82.995, -39.051, 0], "to": [-0.047, 0, 0], "ti": [0.049, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [82.848, -39.051, 0], "to": [-0.049, 0, 0], "ti": [0.05, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [82.698, -39.051, 0], "to": [-0.05, 0, 0], "ti": [0.047, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [82.551, -39.051, 0], "to": [-0.047, 0, 0], "ti": [0.043, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [82.414, -39.051, 0], "to": [-0.043, 0, 0], "ti": [0.038, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [82.291, -39.051, 0], "to": [-0.038, 0, 0], "ti": [0.031, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [82.188, -39.051, 0], "to": [-0.031, 0, 0], "ti": [0.023, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [82.107, -39.051, 0], "to": [-0.023, 0, 0], "ti": [0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [82.05, -39.051, 0], "to": [-0.015, 0, 0], "ti": [0.006, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [82.019, -39.051, 0], "to": [-0.006, 0, 0], "ti": [-0.002, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [82.013, -39.051, 0], "to": [0.002, 0, 0], "ti": [-0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [82.031, -39.051, 0], "to": [0.009, 0, 0], "ti": [-0.016, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [82.07, -39.051, 0], "to": [0.016, 0, 0], "ti": [-0.022, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [82.127, -39.051, 0], "to": [0.022, 0, 0], "ti": [-0.026, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [82.199, -39.051, 0], "to": [0.026, 0, 0], "ti": [-0.029, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [82.282, -39.051, 0], "to": [0.029, 0, 0], "ti": [-0.03, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [82.371, -39.051, 0], "to": [0.03, 0, 0], "ti": [-0.03, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [82.462, -39.051, 0], "to": [0.03, 0, 0], "ti": [-0.029, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [82.551, -39.051, 0], "to": [0.029, 0, 0], "ti": [-0.026, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [82.634, -39.051, 0], "to": [0.026, 0, 0], "ti": [-0.023, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111, "s": [82.709, -39.051, 0], "to": [0.023, 0, 0], "ti": [-0.019, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [82.771, -39.051, 0], "to": [0.019, 0, 0], "ti": [-0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [82.821, -39.051, 0], "to": [0.014, 0, 0], "ti": [-0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [82.855, -39.051, 0], "to": [0.009, 0, 0], "ti": [-0.004, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [82.874, -39.051, 0], "to": [0.004, 0, 0], "ti": [0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [82.877, -39.051, 0], "to": [-0.001, 0, 0], "ti": [0.006, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117, "s": [82.867, -39.051, 0], "to": [-0.006, 0, 0], "ti": [0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [82.843, -39.051, 0], "to": [-0.01, 0, 0], "ti": [0.013, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [82.808, -39.051, 0], "to": [-0.013, 0, 0], "ti": [0.043, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [82.764, -39.051, 0], "to": [-0.043, 0, 0], "ti": [-0.131, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121, "s": [82.551, -39.051, 0], "to": [0.131, 0, 0], "ti": [-0.334, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [83.552, -39.051, 0], "to": [0.334, 0, 0], "ti": [-0.334, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [84.558, -39.051, 0], "to": [0.334, 0, 0], "ti": [-0.332, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [85.554, -39.051, 0], "to": [0.332, 0, 0], "ti": [-0.333, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [86.548, -39.051, 0], "to": [0.333, 0, 0], "ti": [-0.334, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126, "s": [87.551, -39.051, 0], "to": [0.334, 0, 0], "ti": [-0.334, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127, "s": [88.551, -39.051, 0], "to": [0.334, 0, 0], "ti": [-0.316, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128, "s": [89.554, -39.051, 0], "to": [0.316, 0, 0], "ti": [-0.275, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 129, "s": [90.449, -39.051, 0], "to": [0.275, 0, 0], "ti": [-0.224, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [91.205, -39.051, 0], "to": [0.224, 0, 0], "ti": [-0.167, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [91.796, -39.051, 0], "to": [0.167, 0, 0], "ti": [-0.107, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [92.209, -39.051, 0], "to": [0.107, 0, 0], "ti": [-0.045, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133, "s": [92.436, -39.051, 0], "to": [0.045, 0, 0], "ti": [0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [92.48, -39.051, 0], "to": [-0.014, 0, 0], "ti": [0.069, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [92.351, -39.051, 0], "to": [-0.069, 0, 0], "ti": [0.117, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136, "s": [92.066, -39.051, 0], "to": [-0.117, 0, 0], "ti": [0.158, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [91.647, -39.051, 0], "to": [-0.158, 0, 0], "ti": [0.188, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138, "s": [91.121, -39.051, 0], "to": [-0.188, 0, 0], "ti": [0.209, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [90.517, -39.051, 0], "to": [-0.209, 0, 0], "ti": [0.219, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [89.867, -39.051, 0], "to": [-0.219, 0, 0], "ti": [0.219, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [89.202, -39.051, 0], "to": [-0.219, 0, 0], "ti": [0.21, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [88.551, -39.051, 0], "to": [-0.21, 0, 0], "ti": [0.192, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143, "s": [87.942, -39.051, 0], "to": [-0.192, 0, 0], "ti": [0.167, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [87.4, -39.051, 0], "to": [-0.167, 0, 0], "ti": [0.136, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [86.942, -39.051, 0], "to": [-0.136, 0, 0], "ti": [0.101, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [86.583, -39.051, 0], "to": [-0.101, 0, 0], "ti": [0.065, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [86.333, -39.051, 0], "to": [-0.065, 0, 0], "ti": [0.027, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148, "s": [86.195, -39.051, 0], "to": [-0.027, 0, 0], "ti": [-0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [86.168, -39.051, 0], "to": [0.009, 0, 0], "ti": [-0.042, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [86.246, -39.051, 0], "to": [0.042, 0, 0], "ti": [-0.071, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [86.419, -39.051, 0], "to": [0.071, 0, 0], "ti": [-0.096, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [86.673, -39.051, 0], "to": [0.096, 0, 0], "ti": [-0.114, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153, "s": [86.992, -39.051, 0], "to": [0.114, 0, 0], "ti": [-0.127, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [87.359, -39.051, 0], "to": [0.127, 0, 0], "ti": [-0.133, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [87.753, -39.051, 0], "to": [0.133, 0, 0], "ti": [-0.133, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156, "s": [88.156, -39.051, 0], "to": [0.133, 0, 0], "ti": [-0.127, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157, "s": [88.551, -39.051, 0], "to": [0.127, 0, 0], "ti": [-0.116, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [88.92, -39.051, 0], "to": [0.116, 0, 0], "ti": [-0.101, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159, "s": [89.249, -39.051, 0], "to": [0.101, 0, 0], "ti": [-0.083, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [89.527, -39.051, 0], "to": [0.083, 0, 0], "ti": [-0.062, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [89.745, -39.051, 0], "to": [0.062, 0, 0], "ti": [-0.039, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162, "s": [89.897, -39.051, 0], "to": [0.039, 0, 0], "ti": [-0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [89.98, -39.051, 0], "to": [0.017, 0, 0], "ti": [0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [89.997, -39.051, 0], "to": [-0.005, 0, 0], "ti": [0.025, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [89.949, -39.051, 0], "to": [-0.025, 0, 0], "ti": [0.043, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166, "s": [89.844, -39.051, 0], "to": [-0.043, 0, 0], "ti": [0.058, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [89.69, -39.051, 0], "to": [-0.058, 0, 0], "ti": [0.069, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168, "s": [89.496, -39.051, 0], "to": [-0.069, 0, 0], "ti": [0.077, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169, "s": [89.274, -39.051, 0], "to": [-0.077, 0, 0], "ti": [0.081, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [89.035, -39.051, 0], "to": [-0.081, 0, 0], "ti": [0.081, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171, "s": [88.79, -39.051, 0], "to": [-0.081, 0, 0], "ti": [0.077, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172, "s": [88.551, -39.051, 0], "to": [-0.077, 0, 0], "ti": [0.071, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [88.327, -39.051, 0], "to": [-0.071, 0, 0], "ti": [0.061, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [88.127, -39.051, 0], "to": [-0.061, 0, 0], "ti": [0.05, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [87.959, -39.051, 0], "to": [-0.05, 0, 0], "ti": [0.037, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176, "s": [87.827, -39.051, 0], "to": [-0.037, 0, 0], "ti": [0.024, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177, "s": [87.735, -39.051, 0], "to": [-0.024, 0, 0], "ti": [0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178, "s": [87.684, -39.051, 0], "to": [-0.01, 0, 0], "ti": [-0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [87.674, -39.051, 0], "to": [0.003, 0, 0], "ti": [-0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [87.703, -39.051, 0], "to": [0.015, 0, 0], "ti": [-0.026, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181, "s": [87.767, -39.051, 0], "to": [0.026, 0, 0], "ti": [-0.035, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182, "s": [87.86, -39.051, 0], "to": [0.035, 0, 0], "ti": [-0.042, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183, "s": [87.978, -39.051, 0], "to": [0.042, 0, 0], "ti": [-0.047, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 184, "s": [88.112, -39.051, 0], "to": [0.047, 0, 0], "ti": [-0.049, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [88.257, -39.051, 0], "to": [0.049, 0, 0], "ti": [-0.049, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 186, "s": [88.406, -39.051, 0], "to": [0.049, 0, 0], "ti": [-0.047, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187, "s": [88.551, -39.051, 0], "to": [0.047, 0, 0], "ti": [-0.043, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188, "s": [88.687, -39.051, 0], "to": [0.043, 0, 0], "ti": [-0.037, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 189, "s": [88.808, -39.051, 0], "to": [0.037, 0, 0], "ti": [-0.03, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [88.91, -39.051, 0], "to": [0.03, 0, 0], "ti": [-0.023, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191, "s": [88.99, -39.051, 0], "to": [0.023, 0, 0], "ti": [-0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192, "s": [89.046, -39.051, 0], "to": [0.014, 0, 0], "ti": [-0.006, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193, "s": [89.077, -39.051, 0], "to": [0.006, 0, 0], "ti": [0.002, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [89.083, -39.051, 0], "to": [-0.002, 0, 0], "ti": [0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [89.065, -39.051, 0], "to": [-0.009, 0, 0], "ti": [0.016, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [89.027, -39.051, 0], "to": [-0.016, 0, 0], "ti": [0.021, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [88.97, -39.051, 0], "to": [-0.021, 0, 0], "ti": [0.025, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198, "s": [88.899, -39.051, 0], "to": [-0.025, 0, 0], "ti": [0.028, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199, "s": [88.817, -39.051, 0], "to": [-0.028, 0, 0], "ti": [0.03, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [88.729, -39.051, 0], "to": [-0.03, 0, 0], "ti": [0.03, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 201, "s": [88.639, -39.051, 0], "to": [-0.03, 0, 0], "ti": [0.028, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [88.551, -39.051, 0], "to": [-0.028, 0, 0], "ti": [0.026, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [88.469, -39.051, 0], "to": [-0.026, 0, 0], "ti": [0.023, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 204, "s": [88.395, -39.051, 0], "to": [-0.023, 0, 0], "ti": [0.018, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [88.333, -39.051, 0], "to": [-0.018, 0, 0], "ti": [0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [88.285, -39.051, 0], "to": [-0.014, 0, 0], "ti": [0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [88.251, -39.051, 0], "to": [-0.009, 0, 0], "ti": [0.004, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208, "s": [88.232, -39.051, 0], "to": [-0.004, 0, 0], "ti": [-0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 209, "s": [88.228, -39.051, 0], "to": [0.001, 0, 0], "ti": [-0.006, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 210, "s": [88.239, -39.051, 0], "to": [0.006, 0, 0], "ti": [-0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 211, "s": [88.262, -39.051, 0], "to": [0.01, 0, 0], "ti": [-0.013, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 212, "s": [88.297, -39.051, 0], "to": [0.013, 0, 0], "ti": [-0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 213, "s": [88.34, -39.051, 0], "to": [0.015, 0, 0], "ti": [-0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 214, "s": [88.39, -39.051, 0], "to": [0.017, 0, 0], "ti": [-0.018, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 215, "s": [88.443, -39.051, 0], "to": [0.018, 0, 0], "ti": [-0.018, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 216, "s": [88.498, -39.051, 0], "to": [0.018, 0, 0], "ti": [-0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 217, "s": [88.551, -39.051, 0], "to": [0.017, 0, 0], "ti": [-0.016, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 218, "s": [88.601, -39.051, 0], "to": [0.016, 0, 0], "ti": [-0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 219, "s": [88.646, -39.051, 0], "to": [0.014, 0, 0], "ti": [-0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 220, "s": [88.683, -39.051, 0], "to": [0.011, 0, 0], "ti": [-0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 221, "s": [88.713, -39.051, 0], "to": [0.008, 0, 0], "ti": [-0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 222, "s": [88.733, -39.051, 0], "to": [0.005, 0, 0], "ti": [-0.002, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 223, "s": [88.744, -39.051, 0], "to": [0.002, 0, 0], "ti": [0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 224, "s": [88.747, -39.051, 0], "to": [-0.001, 0, 0], "ti": [0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 225, "s": [88.74, -39.051, 0], "to": [-0.003, 0, 0], "ti": [0.006, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 226, "s": [88.726, -39.051, 0], "to": [-0.006, 0, 0], "ti": [0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 227, "s": [88.705, -39.051, 0], "to": [-0.008, 0, 0], "ti": [0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 228, "s": [88.679, -39.051, 0], "to": [-0.009, 0, 0], "ti": [0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 229, "s": [88.649, -39.051, 0], "to": [-0.01, 0, 0], "ti": [0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 230, "s": [88.617, -39.051, 0], "to": [-0.011, 0, 0], "ti": [0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 231, "s": [88.583, -39.051, 0], "to": [-0.011, 0, 0], "ti": [0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 232, "s": [88.551, -39.051, 0], "to": [-0.01, 0, 0], "ti": [0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 233, "s": [88.521, -39.051, 0], "to": [-0.01, 0, 0], "ti": [0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 234, "s": [88.494, -39.051, 0], "to": [-0.008, 0, 0], "ti": [0.007, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 235, "s": [88.471, -39.051, 0], "to": [-0.007, 0, 0], "ti": [0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 236, "s": [88.453, -39.051, 0], "to": [-0.005, 0, 0], "ti": [0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 237, "s": [88.441, -39.051, 0], "to": [-0.003, 0, 0], "ti": [0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 238, "s": [88.434, -39.051, 0], "to": [-0.001, 0, 0], "ti": [0, 0, 0]}, {"t": 239, "s": [88.432, -39.051, 0]}], "ix": 2}, "a": {"a": 0, "k": [8.75, 8.75, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.694, 0], [0, 4.694], [4.694, 0], [0, -4.694]], "o": [[4.694, 0], [0, -4.694], [-4.694, 0], [0, 4.694]], "v": [[0, 8.5], [8.5, 0], [0, -8.5], [-8.5, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [8.75, 8.75], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Cloud 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.43, "y": 1}, "o": {"x": 0.57, "y": 0}, "t": 0, "s": [302.145, 338.274, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.43, "y": 1}, "o": {"x": 0.57, "y": 0}, "t": 120, "s": [228.145, 338.274, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 240, "s": [302.145, 338.274, 0]}], "ix": 2}, "a": {"a": 0, "k": [197.395, 83.381, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -41.274], [-41.275, 0], [0, 0], [0, 29.793], [29.793, 0], [8.865, -17.585], [0, 3.488], [42.728, 0], [9.301, -32.119], [28.485, 0]], "o": [[0, 41.42], [0, 0], [29.938, 0], [0, -29.939], [-21.219, 0], [0.436, -3.488], [0, -42.728], [-35.171, 0], [-12.644, -23.253], [-41.42, 0]], "v": [[-197.145, 8.284], [-122.298, 83.131], [143.08, 83.131], [197.145, 29.067], [143.08, -24.997], [94.684, 4.796], [95.41, -5.668], [17.949, -83.131], [-56.316, -27.468], [-122.153, -66.563]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.889999988032, 0.823999980852, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Pink').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [197.395, 83.381], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Cloud 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.43, "y": 1}, "o": {"x": 0.57, "y": 0}, "t": 0, "s": [671.806, 826.665, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.43, "y": 1}, "o": {"x": 0.57, "y": 0}, "t": 120, "s": [771.806, 826.665, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 240, "s": [671.806, 826.665, 0]}], "ix": 2}, "a": {"a": 0, "k": [116.073, 49.089, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -24.249], [24.248, 0], [0, 0], [0, 17.504], [-17.504, 0], [-5.209, -10.331], [0, 2.049], [-25.103, 0], [-5.465, -18.869], [-16.735, 0]], "o": [[0, 24.334], [0, 0], [-17.589, 0], [0, -17.589], [12.466, 0], [-0.257, -2.049], [0, -25.103], [20.663, 0], [7.428, -13.661], [24.334, 0]], "v": [[115.823, 4.867], [71.85, 48.839], [-84.06, 48.839], [-115.823, 17.076], [-84.06, -14.686], [-55.627, 2.817], [-56.054, -3.33], [-10.544, -48.839], [33.086, -16.138], [71.764, -39.106]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.889999988032, 0.823999980852, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Pink').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [116.072, 49.089], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "<PERSON>earm Left", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 120, "s": [9.094]}, {"t": 240, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [706.484, 264, 0], "ix": 2}, "a": {"a": 0, "k": [49.372, 45.95, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.976, -0.977], [-0.977, -0.977]], "o": [[-0.976, -0.977], [-0.977, 0.976], [0, 0]], "v": [[-48.482, -52.018], [-52.017, -52.018], [-52.017, -48.482]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.977, 0.976], [0.976, 0.976]], "o": [[0.976, 0.976], [0.976, -0.977], [0, 0]], "v": [[48.482, 52.018], [52.018, 52.018], [52.018, 48.482]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-52.017, -48.482], [48.482, 52.018], [52.018, 48.482], [-48.482, -52.018]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [157, 89.7], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.2, 15.7], [-26.7, -3.7], [-7.6, -9.8]], "o": [[0, 0], [0, 0], [0, 0], [-10.3, -10.4], [3.7, -26.7], [13.2, 1.8], [0, 0]], "v": [[104.15, 36.65], [34.85, 105.45], [-88.55, -18.75], [-88.25, -19.05], [-101.95, -60.15], [-46.85, -101.75], [-14.85, -83.15]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [104.4, 105.7], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Hand Left", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 120, "s": [18.811]}, {"t": 240, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [191.705, 175.949, 0], "ix": 2}, "a": {"a": 0, "k": [41.998, 37.25, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-30.428, 25.425], [30.428, -3.902], [15.691, -25.425], [-21.728, -7.276]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [40.774, 25.676], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.566, -0.462], [-0.462, 1.567]], "o": [[-0.462, 1.566], [1.566, 0.461], [0, 0]], "v": [[-8.237, 40.178], [-6.237, 43.85], [-2.565, 41.849]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.051, 0.403]], "o": [[0, 0], [0.114, -0.389], [0, 0]], "v": [[5.715, 3.29], [8.551, 4.125], [8.649, 2.923]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [1.62, -0.202], [-0.202, -1.62]], "o": [[-0.203, -1.62], [-1.62, 0.203], [0, 0]], "v": [[3.09, -41.542], [-0.21, -44.109], [-2.777, -40.808]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.565, 41.849], [8.551, 4.125], [2.879, 2.454], [-8.237, 40.178]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[8.649, 2.923], [3.09, -41.542], [-2.777, -40.808], [2.782, 3.657]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [97.182, 154.952], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 7, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.529, -0.571], [-0.57, 1.53]], "o": [[-0.571, 1.53], [1.531, 0.571], [0, 0]], "v": [[-2.904, 19.418], [-1.169, 23.222], [2.635, 21.486]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.103, 0.532]], "o": [[0, 0], [0.189, -0.508], [0, 0]], "v": [[3.177, 11.583], [5.947, 12.617], [6.08, 11.028]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [1.603, -0.306], [-0.306, -1.604]], "o": [[-0.307, -1.604], [-1.605, 0.307], [0, 0]], "v": [[-0.068, -21.138], [-3.527, -23.487], [-5.877, -20.028]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.635, 21.486], [5.947, 12.617], [0.408, 10.549], [-2.904, 19.418]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.08, 11.028], [-0.068, -21.138], [-5.877, -20.028], [0.273, 12.138]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [65.78, 137.79], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 7, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.37, 1.59], [1.591, 0.37]], "o": [[1.59, 0.37], [0.37, -1.59], [0, 0]], "v": [[6.981, -10.243], [10.529, -12.452], [8.32, -16.001]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -0.225]], "o": [[0, 0], [-0.051, 0.22], [0, 0]], "v": [[-3.703, -6.027], [-6.583, -6.697], [-6.659, -6.027]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0.223]], "o": [[0, 0], [0.05, -0.218], [0, 0]], "v": [[-3.703, -5.79], [-0.821, -5.126], [-0.747, -5.79]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.419, -0.666], [-0.766, -0.173]], "o": [[0, 0], [-0.177, 0.767], [0.419, 0.666], [0, 0]], "v": [[-7.841, 12.186], [-10.722, 11.522], [-10.344, 13.759], [-8.492, 15.069]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.358, 1.593], [1.593, 0.358]], "o": [[1.593, 0.359], [0.359, -1.593], [0, 0]], "v": [[0.969, 17.197], [4.503, 14.962], [2.268, 11.43]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [1.549, -6.662], [0, 0], [-3.482, -0.81]], "o": [[-6.688, -1.555], [0, 0], [0.816, -3.508], [0, 0]], "v": [[8.32, -16.001], [-6.583, -6.697], [-0.823, -5.356], [6.981, -10.243]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.659, -6.027], [-6.659, -6.025], [-6.659, -6.025], [-6.659, -6.023], [-6.659, -6.022], [-6.659, -6.022], [-6.659, -6.02], [-6.659, -6.02], [-6.659, -6.018], [-6.659, -6.017], [-6.659, -6.017], [-6.659, -6.015], [-6.659, -6.014], [-6.659, -6.014], [-6.659, -6.012], [-6.659, -6.012], [-6.659, -6.01], [-6.659, -6.009], [-6.659, -6.009], [-6.659, -6.007], [-6.659, -6.007], [-6.659, -6.005], [-6.659, -6.004], [-6.659, -6.004], [-6.659, -6.002], [-6.659, -6.001], [-6.659, -6.001], [-6.659, -5.999], [-6.659, -5.999], [-6.659, -5.997], [-6.659, -5.996], [-6.659, -5.996], [-6.659, -5.994], [-6.659, -5.993], [-6.659, -5.993], [-6.659, -5.991], [-6.659, -5.991], [-6.659, -5.989], [-6.659, -5.988], [-6.659, -5.988], [-6.659, -5.986], [-6.659, -5.986], [-6.659, -5.984], [-6.659, -5.983], [-6.659, -5.983], [-6.659, -5.981], [-6.659, -5.98], [-6.659, -5.98], [-6.659, -5.978], [-6.659, -5.978], [-6.659, -5.976], [-6.659, -5.975], [-6.659, -5.975], [-6.659, -5.973], [-6.659, -5.973], [-6.659, -5.971], [-6.659, -5.97], [-6.659, -5.97], [-6.659, -5.968], [-6.659, -5.967], [-6.659, -5.967], [-6.659, -5.965], [-6.659, -5.965], [-6.659, -5.963], [-6.659, -5.962], [-6.659, -5.962], [-6.659, -5.96], [-6.659, -5.96], [-6.659, -5.958], [-6.659, -5.957], [-6.659, -5.957], [-6.659, -5.955], [-6.659, -5.954], [-6.659, -5.954], [-6.659, -5.952], [-6.659, -5.952], [-6.659, -5.95], [-6.659, -5.949], [-6.659, -5.949], [-6.659, -5.947], [-6.659, -5.947], [-6.659, -5.945], [-6.659, -5.944], [-6.659, -5.944], [-6.659, -5.942], [-6.659, -5.941], [-6.659, -5.941], [-6.659, -5.939], [-6.659, -5.939], [-6.659, -5.937], [-6.659, -5.936], [-6.659, -5.936], [-6.659, -5.934], [-6.659, -5.934], [-6.659, -5.932], [-6.659, -5.931], [-6.659, -5.931], [-6.659, -5.929], [-6.659, -5.928], [-6.659, -5.928], [-6.659, -5.926], [-6.659, -5.926], [-6.659, -5.924], [-6.659, -5.923], [-6.659, -5.923], [-6.659, -5.921], [-6.659, -5.921], [-6.659, -5.919], [-6.659, -5.918], [-6.659, -5.918], [-6.659, -5.916], [-6.659, -5.915], [-6.659, -5.915], [-6.659, -5.913], [-6.659, -5.913], [-6.659, -5.911], [-6.659, -5.91], [-6.659, -5.91], [-6.659, -5.908], [-6.659, -5.908], [-6.659, -5.906], [-6.659, -5.905], [-6.659, -5.905], [-6.659, -5.903], [-6.659, -5.902], [-6.659, -5.902], [-6.659, -5.9], [-6.659, -5.9], [-6.659, -5.898], [-6.659, -5.897], [-6.659, -5.897], [-6.659, -5.895], [-6.659, -5.895], [-6.659, -5.893], [-6.659, -5.892], [-6.659, -5.892], [-6.659, -5.89], [-6.659, -5.889], [-6.659, -5.889], [-6.659, -5.887], [-6.659, -5.887], [-6.659, -5.885], [-6.659, -5.884], [-6.659, -5.884], [-6.659, -5.882], [-6.659, -5.882], [-6.659, -5.88], [-6.659, -5.879], [-6.659, -5.879], [-6.659, -5.877], [-6.659, -5.876], [-6.659, -5.876], [-6.659, -5.874], [-6.659, -5.874], [-6.659, -5.872], [-6.659, -5.871], [-6.659, -5.871], [-6.659, -5.869], [-6.659, -5.868], [-6.659, -5.868], [-6.659, -5.866], [-6.659, -5.866], [-6.659, -5.864], [-6.659, -5.863], [-6.659, -5.863], [-6.659, -5.861], [-6.659, -5.861], [-6.659, -5.859], [-6.659, -5.858], [-6.659, -5.858], [-6.659, -5.856], [-6.659, -5.855], [-6.659, -5.855], [-6.659, -5.853], [-6.659, -5.853], [-6.659, -5.851], [-6.659, -5.85], [-6.659, -5.85], [-6.659, -5.848], [-6.659, -5.848], [-6.659, -5.846], [-6.659, -5.845], [-6.659, -5.845], [-6.659, -5.843], [-6.659, -5.842], [-6.659, -5.842], [-6.659, -5.84], [-6.659, -5.84], [-6.659, -5.838], [-6.659, -5.837], [-6.659, -5.837], [-6.659, -5.835], [-6.659, -5.835], [-6.659, -5.833], [-6.659, -5.832], [-6.659, -5.832], [-6.659, -5.83], [-6.659, -5.829], [-6.659, -5.829], [-6.659, -5.827], [-6.659, -5.827], [-6.659, -5.825], [-6.659, -5.824], [-6.659, -5.824], [-6.659, -5.822], [-6.659, -5.822], [-6.659, -5.82], [-6.659, -5.819], [-6.659, -5.819], [-6.659, -5.817], [-6.659, -5.816], [-6.659, -5.816], [-6.659, -5.814], [-6.659, -5.814], [-6.659, -5.812], [-6.659, -5.811], [-6.659, -5.811], [-6.659, -5.809], [-6.659, -5.809], [-6.659, -5.807], [-6.659, -5.806], [-6.659, -5.806], [-6.659, -5.804], [-6.659, -5.803], [-6.659, -5.803], [-6.659, -5.801], [-6.659, -5.801], [-6.659, -5.799], [-6.659, -5.798], [-6.659, -5.798], [-6.659, -5.796], [-6.659, -5.796], [-6.659, -5.794], [-6.659, -5.793], [-6.659, -5.793], [-6.659, -5.791], [-6.659, -5.79], [-6.659, -5.79], [-0.747, -5.79], [-0.747, -5.79], [-0.747, -5.791], [-0.747, -5.793], [-0.747, -5.793], [-0.747, -5.794], [-0.747, -5.796], [-0.747, -5.796], [-0.747, -5.798], [-0.747, -5.798], [-0.747, -5.799], [-0.747, -5.801], [-0.747, -5.801], [-0.747, -5.803], [-0.747, -5.803], [-0.747, -5.804], [-0.747, -5.806], [-0.747, -5.806], [-0.747, -5.807], [-0.747, -5.809], [-0.747, -5.809], [-0.747, -5.811], [-0.747, -5.811], [-0.747, -5.812], [-0.747, -5.814], [-0.747, -5.814], [-0.747, -5.816], [-0.747, -5.816], [-0.747, -5.817], [-0.747, -5.819], [-0.747, -5.819], [-0.747, -5.82], [-0.747, -5.822], [-0.747, -5.822], [-0.747, -5.824], [-0.747, -5.824], [-0.747, -5.825], [-0.747, -5.827], [-0.747, -5.827], [-0.747, -5.829], [-0.747, -5.829], [-0.747, -5.83], [-0.747, -5.832], [-0.747, -5.832], [-0.747, -5.833], [-0.747, -5.835], [-0.747, -5.835], [-0.747, -5.837], [-0.747, -5.837], [-0.747, -5.838], [-0.747, -5.84], [-0.747, -5.84], [-0.747, -5.842], [-0.747, -5.842], [-0.747, -5.843], [-0.747, -5.845], [-0.747, -5.845], [-0.747, -5.846], [-0.747, -5.848], [-0.747, -5.848], [-0.747, -5.85], [-0.747, -5.85], [-0.747, -5.851], [-0.747, -5.853], [-0.747, -5.853], [-0.747, -5.855], [-0.747, -5.855], [-0.747, -5.856], [-0.747, -5.858], [-0.747, -5.858], [-0.747, -5.859], [-0.747, -5.861], [-0.747, -5.861], [-0.747, -5.863], [-0.747, -5.863], [-0.747, -5.864], [-0.747, -5.866], [-0.747, -5.866], [-0.747, -5.868], [-0.747, -5.868], [-0.747, -5.869], [-0.747, -5.871], [-0.747, -5.871], [-0.747, -5.872], [-0.747, -5.874], [-0.747, -5.874], [-0.747, -5.876], [-0.747, -5.876], [-0.747, -5.877], [-0.747, -5.879], [-0.747, -5.879], [-0.747, -5.88], [-0.747, -5.882], [-0.747, -5.882], [-0.747, -5.884], [-0.747, -5.884], [-0.747, -5.885], [-0.747, -5.887], [-0.747, -5.887], [-0.747, -5.889], [-0.747, -5.889], [-0.747, -5.89], [-0.747, -5.892], [-0.747, -5.892], [-0.747, -5.893], [-0.747, -5.895], [-0.747, -5.895], [-0.747, -5.897], [-0.747, -5.897], [-0.747, -5.898], [-0.747, -5.9], [-0.747, -5.9], [-0.747, -5.902], [-0.747, -5.902], [-0.747, -5.903], [-0.747, -5.905], [-0.747, -5.905], [-0.747, -5.906], [-0.747, -5.908], [-0.747, -5.908], [-0.747, -5.91], [-0.747, -5.91], [-0.747, -5.911], [-0.747, -5.913], [-0.747, -5.913], [-0.747, -5.915], [-0.747, -5.915], [-0.747, -5.916], [-0.747, -5.918], [-0.747, -5.918], [-0.747, -5.919], [-0.747, -5.921], [-0.747, -5.921], [-0.747, -5.923], [-0.747, -5.923], [-0.747, -5.924], [-0.747, -5.926], [-0.747, -5.926], [-0.747, -5.928], [-0.747, -5.928], [-0.747, -5.929], [-0.747, -5.931], [-0.747, -5.931], [-0.747, -5.932], [-0.747, -5.934], [-0.747, -5.934], [-0.747, -5.936], [-0.747, -5.936], [-0.747, -5.937], [-0.747, -5.939], [-0.747, -5.939], [-0.747, -5.941], [-0.747, -5.941], [-0.747, -5.942], [-0.747, -5.944], [-0.747, -5.944], [-0.747, -5.945], [-0.747, -5.947], [-0.747, -5.947], [-0.747, -5.949], [-0.747, -5.949], [-0.747, -5.95], [-0.747, -5.952], [-0.747, -5.952], [-0.747, -5.954], [-0.747, -5.954], [-0.747, -5.955], [-0.747, -5.957], [-0.747, -5.957], [-0.747, -5.958], [-0.747, -5.96], [-0.747, -5.96], [-0.747, -5.962], [-0.747, -5.962], [-0.747, -5.963], [-0.747, -5.965], [-0.747, -5.965], [-0.747, -5.967], [-0.747, -5.967], [-0.747, -5.968], [-0.747, -5.97], [-0.747, -5.97], [-0.747, -5.971], [-0.747, -5.973], [-0.747, -5.973], [-0.747, -5.975], [-0.747, -5.975], [-0.747, -5.976], [-0.747, -5.978], [-0.747, -5.978], [-0.747, -5.98], [-0.747, -5.98], [-0.747, -5.981], [-0.747, -5.983], [-0.747, -5.983], [-0.747, -5.984], [-0.747, -5.986], [-0.747, -5.986], [-0.747, -5.988], [-0.747, -5.988], [-0.747, -5.989], [-0.747, -5.991], [-0.747, -5.991], [-0.747, -5.993], [-0.747, -5.993], [-0.747, -5.994], [-0.747, -5.996], [-0.747, -5.996], [-0.747, -5.997], [-0.747, -5.999], [-0.747, -5.999], [-0.747, -6.001], [-0.747, -6.001], [-0.747, -6.002], [-0.747, -6.004], [-0.747, -6.004], [-0.747, -6.005], [-0.747, -6.007], [-0.747, -6.007], [-0.747, -6.009], [-0.747, -6.009], [-0.747, -6.01], [-0.747, -6.012], [-0.747, -6.012], [-0.747, -6.014], [-0.747, -6.014], [-0.747, -6.015], [-0.747, -6.017], [-0.747, -6.017], [-0.747, -6.018], [-0.747, -6.02], [-0.747, -6.02], [-0.747, -6.022], [-0.747, -6.022], [-0.747, -6.023], [-0.747, -6.025], [-0.747, -6.025], [-0.747, -6.027]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.583, -6.454], [-10.722, 11.522], [-4.961, 12.848], [-0.821, -5.126]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.492, 15.069], [0.969, 17.197], [2.268, 11.43], [-7.193, 9.301]], "c": true}, "ix": 2}, "nm": "Path 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [116.061, 197.854], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 11, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.449, 1.57], [1.57, 0.449]], "o": [[1.57, 0.449], [0.449, -1.571], [0, 0]], "v": [[7.643, -10.055], [11.298, -12.084], [9.267, -15.74]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -0.269]], "o": [[0, 0], [-0.072, 0.258], [0, 0]], "v": [[-3.253, -6.392], [-6.1, -7.188], [-6.209, -6.392]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.138, -0.482]], "o": [[0, 0], [-0.355, 0.354], [0, 0]], "v": [[-3.371, -6.156], [-5.461, -8.246], [-6.212, -6.971]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-1.563, -0.455]], "o": [[0, 0], [-0.45, 1.565], [0, 0]], "v": [[-8.456, 11.583], [-11.297, 10.768], [-9.282, 14.421]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.456, 1.567], [1.568, 0.456]], "o": [[1.568, 0.456], [0.457, -1.568], [0, 0]], "v": [[0.06, 17.141], [3.725, 15.129], [1.713, 11.464]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [1.873, -6.71], [0, 0], [-3.436, -0.981]], "o": [[-6.498, -1.856], [0, 0], [0.966, -3.46], [0, 0]], "v": [[9.267, -15.74], [-6.1, -7.188], [-0.405, -5.598], [7.643, -10.055]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.016, 0.161], [-0.191, 0.384], [-0.053, 0.06], [0.052, -0.052], [0, 0], [-0.081, 0.091], [-0.181, 0.363], [-0.028, 0.28], [-0.001, 0.049], [0, 0.006]], "o": [[0, 0.084], [0.025, -0.242], [0.167, -0.333], [0.047, -0.053], [0, 0], [0.007, -0.007], [0.086, -0.096], [0.207, -0.414], [0.012, -0.119], [0.001, -0.047], [0, 0]], "v": [[-6.209, -6.392], [-6.196, -6.616], [-5.912, -7.596], [-5.5, -8.204], [-5.461, -8.246], [-1.28, -4.066], [-1.104, -4.249], [-0.623, -4.953], [-0.312, -6.029], [-0.296, -6.294], [-0.295, -6.392]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-6.212, -6.971], [-11.297, 10.768], [-5.614, 12.397], [-0.529, -5.341]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.282, 14.421], [0.06, 17.141], [1.713, 11.464], [-7.629, 8.744]], "c": true}, "ix": 2}, "nm": "Path 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [84.389, 209.1], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 11, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.775, 1.437], [1.437, 0.774]], "o": [[1.437, 0.774], [0.773, -1.438], [0, 0]], "v": [[9.141, -8.677], [13.145, -9.878], [11.944, -13.883]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -0.479]], "o": [[0, 0], [-0.223, 0.424], [0, 0]], "v": [[-2.23, -7.259], [-4.847, -8.634], [-5.186, -7.259]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0.476]], "o": [[0, 0], [0.22, -0.422], [0, 0]], "v": [[-2.348, -7.023], [0.274, -5.655], [0.608, -7.023]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.236, -0.748], [-0.695, -0.362]], "o": [[0, 0], [-0.363, 0.696], [0.236, 0.749], [0, 0]], "v": [[-10.863, 9.297], [-13.483, 7.929], [-13.683, 10.184], [-12.228, 11.919]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.754, 1.448], [1.448, 0.754]], "o": [[1.448, 0.754], [0.754, -1.449], [0, 0]], "v": [[-3.595, 16.413], [0.392, 15.156], [-0.864, 11.168]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [3.227, -6.148], [0, 0], [-3.126, -1.683]], "o": [[-6.099, -3.284], [0, 0], [1.739, -3.312], [0, 0]], "v": [[11.944, -13.883], [-4.847, -8.634], [0.387, -5.885], [9.141, -8.677]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.074, 0.265], [-0.117, 0.195], [-0.072, 0.082], [-0.017, 0.017], [0.007, -0.007], [0.022, -0.023], [0.043, -0.049], [0.124, -0.207], [0.081, -0.289], [0, -0.266], [0, 0], [0.074, -0.265], [0.118, -0.194], [0.072, -0.082], [0.017, -0.017], [-0.006, 0.006], [-0.022, 0.023], [-0.043, 0.049], [-0.124, 0.207], [-0.081, 0.289], [0, 0.266]], "o": [[0, -0.236], [0.076, -0.266], [0.113, -0.189], [0.036, -0.041], [0.016, -0.016], [0, 0.001], [-0.025, 0.025], [-0.087, 0.098], [-0.129, 0.214], [-0.081, 0.288], [0, 0], [0, 0.237], [-0.074, 0.266], [-0.113, 0.19], [-0.038, 0.042], [-0.015, 0.016], [0.001, -0.001], [0.024, -0.025], [0.086, -0.098], [0.128, -0.214], [0.081, -0.288], [0, 0]], "v": [[-5.186, -7.259], [-5.082, -8.017], [-4.783, -8.71], [-4.484, -9.119], [-4.402, -9.209], [-4.381, -9.231], [-4.423, -9.187], [-4.527, -9.074], [-4.864, -8.614], [-5.189, -7.859], [-5.304, -7.023], [0.608, -7.023], [0.504, -6.265], [0.205, -5.573], [-0.092, -5.163], [-0.176, -5.073], [-0.199, -5.051], [-0.155, -5.095], [-0.049, -5.208], [0.287, -5.668], [0.613, -6.423], [0.727, -7.259]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.968, -8.39], [-13.483, 7.929], [-8.241, 10.664], [0.274, -5.655]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-12.228, 11.919], [-3.595, 16.413], [-0.864, 11.168], [-9.497, 6.675]], "c": true}, "ix": 2}, "nm": "Path 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [48.008, 190.572], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 11, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-17.621, -4.257], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-4.257, 17.738]], "v": [[7.332, 32.521], [21.168, -24.834], [-10.762, -32.521], [-16.911, -7.213]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [106.444, 184.377], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-17.62, -4.257], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-4.258, 17.738]], "v": [[7.332, 32.521], [21.168, -24.833], [-10.762, -32.521], [-16.91, -7.213]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [76.289, 195.729], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-16.556, -7.332], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-7.332, 16.437]], "v": [[0.059, 33.585], [23.947, -20.222], [-5.972, -33.585], [-16.615, -9.697]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [42.762, 175.981], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 2, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-16.438, 23.297], [16.438, 23.297], [16.438, -23.297], [-16.438, -23.297]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [48.761, 138.417], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-16.438, 26.076], [16.438, 26.076], [16.438, -26.076], [-16.438, -26.076]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [80.974, 147.294], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 2, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-16.438, 28.796], [16.438, 28.796], [16.438, -28.796], [-16.438, -28.796]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [111.308, 131.72], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 2, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.186, 33.652], [27.498, 19.522], [2.186, -33.652], [-27.498, -19.522]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [34.499, 98.909], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 2, "cix": 2, "bm": 0, "ix": 13, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.618, 35.494], [29.201, 20.348], [2.617, -35.494], [-29.201, -20.348]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [71.248, 97.681], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 14", "np": 2, "cix": 2, "bm": 0, "ix": 14, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-21.168, -21.287], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-21.286, 21.169]], "v": [[-42.572, 54.457], [-40.444, 56.704], [63.741, 45.943], [-7.805, -56.704], [-42.337, -22.41]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [63.99, 56.955], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 15", "np": 2, "cix": 2, "bm": 0, "ix": 15, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Static", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [725.774, 638.178, 0], "ix": 2}, "a": {"a": 0, "k": [581.438, 412.927, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.381, 0.026], [0.026, -1.381]], "o": [[0.026, -1.38], [-1.381, -0.026], [0, 0]], "v": [[21.55, -9.003], [19.098, -11.55], [16.551, -9.098]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [1.381, 0.026], [0.026, -1.38]], "o": [[0.026, -1.381], [-1.379, -0.026], [0, 0]], "v": [[-16.35, -9.702], [-18.803, -12.249], [-21.349, -9.798]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [9.115, 0.174], [0, 0], [-0.227, 11.877]], "o": [[-0.174, 9.123], [0, 0], [11.885, 0.226], [0, 0]], "v": [[16.551, -9.098], [-0.202, 7.051], [-0.298, 12.049], [21.55, -9.003]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.174, 9.116], [0, 0], [-11.876, -0.226]], "o": [[-9.124, -0.174], [0, 0], [-0.227, 11.884], [0, 0]], "v": [[-0.202, 7.051], [-16.35, -9.702], [-21.349, -9.798], [-0.298, 12.049]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [823.877, 79.9], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.381, 0.026], [0.026, -1.381]], "o": [[0.026, -1.38], [-1.381, -0.026], [0, 0]], "v": [[21.55, -9.003], [19.098, -11.55], [16.551, -9.098]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [1.381, 0.026], [0.026, -1.38]], "o": [[0.026, -1.381], [-1.379, -0.026], [0, 0]], "v": [[-16.35, -9.702], [-18.803, -12.249], [-21.349, -9.798]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [9.115, 0.174], [0, 0], [-0.227, 11.877]], "o": [[-0.174, 9.123], [0, 0], [11.885, 0.226], [0, 0]], "v": [[16.551, -9.098], [-0.202, 7.051], [-0.298, 12.049], [21.55, -9.003]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.174, 9.116], [0, 0], [-11.876, -0.226]], "o": [[-9.124, -0.174], [0, 0], [-0.227, 11.884], [0, 0]], "v": [[-0.202, 7.051], [-16.35, -9.702], [-21.349, -9.798], [-0.298, 12.049]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [785.976, 79.9], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 6, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.2, -23.4], [26.701, 3.7], [4, 1.7], [0, 0], [0, 0], [0, 0]], "o": [[-3.699, 26.7], [-4.599, -0.6], [0, 0], [0, 0], [0, 0], [21.5, 7.6]], "v": [[112.7, 38.1], [57.6, 79.8], [44.7, 76.2], [-115.9, 6], [-76.8, -83.5], [80.6, -14.7]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [659.826, 83.75], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -1.38], [-1.381, 0]], "o": [[-1.381, 0], [0, 1.381], [0, 0]], "v": [[-557.55, -2.5], [-560.05, 0], [-557.55, 2.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 1.381], [1.38, 0]], "o": [[1.38, 0], [0, -1.38], [0, 0]], "v": [[557.55, 2.5], [560.05, 0], [557.55, -2.5]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-557.55, 2.5], [557.55, 2.5], [557.55, -2.5], [-557.55, -2.5]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [602.576, 821.749], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 5, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.253, 0.581], [0.58, -1.252]], "o": [[0.58, -1.253], [-1.253, -0.58], [0, 0]], "v": [[9.919, -15.448], [8.702, -18.768], [5.383, -17.552]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.252, -0.58], [-0.581, 1.252]], "o": [[-0.58, 1.253], [1.253, 0.581], [0, 0]], "v": [[-9.918, 15.448], [-8.702, 18.767], [-5.381, 17.552]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.383, -17.552], [-9.918, 15.448], [-5.381, 17.552], [9.919, -15.448]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [1031.776, 757.85], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 5, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-17.65, 26.801], [-18.35, -50.301], [18.35, 50.301]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [861.577, 450.75], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.48, -1.294], [-1.295, 0.481]], "o": [[-1.295, 0.48], [0.481, 1.294], [0, 0]], "v": [[-32.52, 9.406], [-33.994, 12.62], [-30.78, 14.093]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0.48, 1.294], [1.295, -0.48]], "o": [[1.294, -0.481], [-0.481, -1.294], [0, 0]], "v": [[32.52, -9.406], [33.994, -12.62], [30.78, -14.094]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-30.78, 14.093], [32.52, -9.406], [30.78, -14.094], [-32.52, 9.406]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [992.676, 547.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 5, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-50.3, 1.349], [50.3, 39.151], [-35.5, -39.151]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [962.026, 691.9], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.1, -2.1], [0, 0]], "o": [[0, 0], [0.1, 2.1]], "v": [[-0.05, 3.15], [-0.05, -3.15]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.889999988032, 0.823999980852, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [898.977, 175.7], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 2, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-25, 18.9], [-32.9, -43.6], [-0.699, -19.5], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.7, -29.1], [43.601, -32.9], [12.6, 16.8], [0, 0], [0, 0]], "v": [[99.4, 142.65], [-98.2, 119.55], [-99.4, -33.25], [-99.3, -33.25], [-60.1, -109.75], [78.3, -90.35], [98.2, -34.65], [98.2, -28.35]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.889999988032, 0.823999980852, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Pink').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [800.726, 207.2], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -1.381], [-1.381, 0]], "o": [[-1.381, 0], [0, 1.381], [0, 0]], "v": [[-45.5, -2.5], [-48, 0], [-45.5, 2.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 1.381], [1.381, 0]], "o": [[1.381, 0], [0, -1.381], [0, 0]], "v": [[45.5, 2.5], [48, 0], [45.5, -2.5]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-45.5, 2.5], [45.5, 2.5], [45.5, -2.5], [-45.5, -2.5]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [774.126, 799.15], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 5, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -6.5], [0, 0], [0, 0], [0, 0], [-2.099, 5.301], [-19.399, 0], [0, 0], [-7, -16.4]], "o": [[0, 0], [0, 0], [0, 0], [0, -6.099], [6.8, -17], [0, 0], [19, 0], [2.5, 5.7]], "v": [[71.95, 6.051], [71.95, 40.25], [-71.95, 40.25], [-71.95, 5.95], [-68.651, -11.25], [-25.851, -40.25], [25.75, -40.25], [68.149, -12.349]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [775.777, 781.499], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 2, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.497, -1.288], [-1.287, -0.497]], "o": [[-1.288, -0.497], [-0.497, 1.288], [0, 0]], "v": [[-119.802, -27.885], [-123.035, -26.453], [-121.604, -23.221]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.286, -0.007]], "o": [[0, 0], [0.267, 0.104], [0, 0]], "v": [[4.596, 22.847], [3.696, 25.179], [4.532, 25.346]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.036, 1.38], [1.38, 0.036]], "o": [[1.38, 0.036], [0.035, -1.381], [0, 0]], "v": [[120.932, 28.346], [123.496, 25.912], [121.061, 23.348]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-121.604, -23.221], [3.696, 25.179], [5.497, 20.515], [-119.802, -27.885]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.532, 25.346], [120.932, 28.346], [121.061, 23.348], [4.661, 20.348]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [1012.229, 769.503], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 7, "cix": 2, "bm": 0, "ix": 13, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -28.199], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [28.4, 0.1]], "v": [[126.5, 35], [126.5, 64.3], [14.6, 64.3], [-126.5, 14.2], [-96.9, -64.3], [30.8, -16.2], [75.3, -16.2]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [1008.526, 758.45], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 14", "np": 2, "cix": 2, "bm": 0, "ix": 14, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -37.8], [3.8, -8.6], [0, 0], [0, 0], [0, 0], [-29.4, 0]], "o": [[0, 10], [0, 0], [0, 0], [0, 0], [9.7, -26], [37.8, -0.1]], "v": [[100.1, -71], [94.1, -42.8], [28.499, 139.5], [-100.1, 92.6], [-32.6, -94.9], [31.6, -139.4]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [1010.627, 606.15], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 15", "np": 2, "cix": 2, "bm": 0, "ix": 15, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [19, 0], [0, 0], [6.8, -18.8], [0, 0], [0, 0]], "o": [[-7.1, -18.2], [0, 0], [-19.399, 0], [0, 0], [0, 0], [0, 0]], "v": [[70.05, 230.55], [27.651, 199.55], [-23.95, 199.55], [-66.75, 231.65], [-70.05, -230.349], [66.851, -231.65]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [773.976, 543.199], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 16", "np": 2, "cix": 2, "bm": 0, "ix": 16, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-151.881, -24], [75.981, 138.5], [151.881, 24.6], [-76.981, -138.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [923.045, 450.249], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 17", "np": 2, "cix": 2, "bm": 0, "ix": 17, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -1.381], [-1.381, 0]], "o": [[-1.381, 0], [0, 1.381], [0, 0]], "v": [[-116.902, -2.5], [-119.402, 0], [-116.902, 2.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 1.381], [1.381, 0]], "o": [[1.381, 0], [0, -1.381], [0, 0]], "v": [[116.902, 2.5], [119.402, 0], [116.902, -2.5]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-116.902, 2.5], [116.902, 2.5], [116.902, -2.5], [-116.902, -2.5]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [336.52, 697.99], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 18", "np": 5, "cix": 2, "bm": 0, "ix": 18, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.195, -0.691], [-0.691, 1.195]], "o": [[-0.691, 1.195], [1.195, 0.69], [0, 0]], "v": [[-73.656, 60.452], [-72.743, 63.868], [-69.328, 62.955]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-35.746, -0.125], [-33.582, 1.127], [-33.581, 1.125]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.893, 0], [0.446, -0.773]], "o": [[0, 0], [-0.447, -0.773], [-0.893, 0], [0, 0]], "v": [[0, -62.057], [2.165, -63.307], [0, -64.557], [-2.165, -63.307]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[35.746, -0.125], [33.581, 1.125], [33.582, 1.127]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.195, 0.69], [0.691, 1.195]], "o": [[0.691, 1.195], [1.196, -0.691], [0, 0]], "v": [[69.328, 62.955], [72.743, 63.868], [73.656, 60.452]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-69.328, 62.955], [-33.582, 1.127], [-37.91, -1.376], [-73.656, 60.452]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-33.581, 1.125], [2.165, -60.808], [-2.165, -63.307], [-37.911, -1.375]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.165, -60.808], [33.581, 1.125], [37.911, -1.375], [2.165, -63.307]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[33.582, 1.127], [69.328, 62.955], [73.656, 60.452], [37.91, -1.376]], "c": true}, "ix": 2}, "nm": "Path 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [336.519, 760.047], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 19", "np": 11, "cix": 2, "bm": 0, "ix": 19, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [60.083, 0], [0, 0], [0, 62.845]], "o": [[0, 60.083], [0, 0], [62.845, 0], [0, 0]], "v": [[108.791, -0.001], [0, 108.792], [0, 113.792], [113.791, -0.001]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 60.083], [0, 0], [-62.845, 0]], "o": [[-60.084, 0], [0, 0], [0, 62.845], [0, 0]], "v": [[0, 108.792], [-108.791, -0.001], [-113.791, -0.001], [0, 113.792]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [-60.084, 0], [0, 0], [0, -62.845]], "o": [[0, -60.083], [0, 0], [-62.845, 0], [0, 0]], "v": [[-108.791, -0.001], [0, -108.791], [0, -113.791], [-113.791, -0.001]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -60.083], [0, 0], [62.845, 0]], "o": [[60.083, 0], [0, 0], [0, -62.845], [0, 0]], "v": [[0, -108.791], [108.791, -0.001], [113.791, -0.001], [0, -113.791]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [336.519, 361.519], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 20", "np": 6, "cix": 2, "bm": 0, "ix": 20, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [121.491, 0], [0, 0], [0, 124.252]], "o": [[0, 121.491], [0, 0], [124.252, 0], [0, 0]], "v": [[219.978, 0], [0, 219.978], [0, 224.978], [224.978, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 121.491], [0, 0], [-124.252, 0]], "o": [[-121.491, 0], [0, 0], [0, 124.252], [0, 0]], "v": [[0, 219.978], [-219.978, 0], [-224.978, 0], [0, 224.978]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [-121.491, 0], [0, 0], [0, -124.252]], "o": [[0, -121.491], [0, 0], [-124.252, 0], [0, 0]], "v": [[-219.978, 0], [0, -219.978], [0, -224.978], [-224.978, 0]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -121.491], [0, 0], [124.252, 0]], "o": [[121.491, 0], [0, 0], [0, -124.252], [0, 0]], "v": [[0, -219.978], [219.978, 0], [224.978, 0], [0, -224.978]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [340.519, 361.518], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 21", "np": 6, "cix": 2, "bm": 0, "ix": 21, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [182.955, 0], [0, 0], [0, 185.716]], "o": [[0, 182.955], [0, 0], [185.716, 0], [0, 0]], "v": [[331.269, 0], [0, 331.269], [0, 336.269], [336.269, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 182.955], [0, 0], [-185.716, 0]], "o": [[-182.955, 0], [0, 0], [0, 185.716], [0, 0]], "v": [[0, 331.269], [-331.269, 0], [-336.269, 0], [0, 336.269]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [-182.955, 0], [0, 0], [0, -185.716]], "o": [[0, -182.955], [0, 0], [-185.716, 0], [0, 0]], "v": [[-331.269, 0], [0, -331.269], [0, -336.269], [-336.269, 0]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -182.955], [0, 0], [185.716, 0]], "o": [[182.955, 0], [0, 0], [0, -185.716], [0, 0]], "v": [[0, -331.269], [331.269, 0], [336.269, 0], [0, -336.269]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [340.519, 361.518], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 22", "np": 6, "cix": 2, "bm": 0, "ix": 22, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Crease Right", "parent": 20, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [176.149, 147.1, 0], "ix": 2}, "a": {"a": 0, "k": [2.5, 34.35, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[2.5, 2.5], [2.5, 66.2]], "c": false}]}, {"i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-13.615, -10.052], [2.5, 66.2]], "c": false}]}, {"i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 120, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[2.5, 2.5], [2.5, 66.2]], "c": false}]}, {"i": {"x": 0.5, "y": 1}, "o": {"x": 0.5, "y": 0}, "t": 180, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-13.615, -10.052], [2.5, 66.2]], "c": false}]}, {"t": 240, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[2.5, 2.5], [2.5, 66.2]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "Hand Right", "parent": 19, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": -30, "s": [-4.324]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 30, "s": [14.756]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 90, "s": [-4.324]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 150, "s": [14.756]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 210, "s": [-4.324]}, {"t": 270, "s": [14.756]}], "ix": 10}, "p": {"a": 0, "k": [209.54, 31.61, 0], "ix": 2}, "a": {"a": 0, "k": [39.241, 179.304, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.117, -1.334], [-1.334, -1.117]], "o": [[-1.334, -1.117], [-1.118, 1.333], [0, 0]], "v": [[-8.057, -10.857], [-12.494, -10.464], [-12.102, -6.027]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.117, 1.333], [1.334, 1.117]], "o": [[1.334, 1.117], [1.117, -1.334], [0, 0]], "v": [[8.057, 10.856], [12.495, 10.465], [12.102, 6.027]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-12.102, -6.027], [8.057, 10.856], [12.102, 6.027], [-8.057, -10.857]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [195.92, 133.301], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.797, -1.546], [-1.546, -0.797]], "o": [[-1.547, -0.797], [-0.797, 1.547], [0, 0]], "v": [[-12.857, -10.171], [-17.1, -8.814], [-15.745, -4.57]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.798, 1.546], [1.546, 0.797]], "o": [[1.546, 0.797], [0.797, -1.546], [0, 0]], "v": [[12.858, 10.171], [17.1, 8.814], [15.744, 4.571]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-15.745, -4.57], [12.858, 10.171], [15.744, 4.571], [-12.857, -10.171]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [166.123, 87.124], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 5, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.434, -1.685], [-1.686, -0.434]], "o": [[-1.684, -0.435], [-0.435, 1.684], [0, 0]], "v": [[-11.183, -6.137], [-15.019, -3.872], [-12.755, -0.036]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.434, 1.685], [1.685, 0.434]], "o": [[1.685, 0.434], [0.435, -1.684], [0, 0]], "v": [[11.183, 6.138], [15.019, 3.874], [12.756, 0.038]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-12.755, -0.036], [11.183, 6.138], [12.756, 0.038], [-11.183, -6.137]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [115.536, 44.536], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 5, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.371, -1.07], [-1.07, -1.372]], "o": [[-1.07, -1.372], [-1.372, 1.07], [0, 0]], "v": [[-4.446, -10.821], [-8.867, -11.366], [-9.413, -6.946]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.371, 1.07], [1.071, 1.371]], "o": [[1.07, 1.371], [1.373, -1.07], [0, 0]], "v": [[4.447, 10.821], [8.867, 11.366], [9.412, 6.946]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.413, -6.946], [4.447, 10.821], [9.412, 6.946], [-4.446, -10.821]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [171.604, 155.539], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 5, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.289, -1.168], [-1.168, -1.289]], "o": [[-1.168, -1.289], [-1.288, 1.169], [0, 0]], "v": [[-6.235, -11.566], [-10.684, -11.784], [-10.902, -7.334]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.289, 1.169], [1.169, 1.289]], "o": [[1.168, 1.289], [1.289, -1.168], [0, 0]], "v": [[6.234, 11.565], [10.683, 11.783], [10.901, 7.333]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-10.902, -7.334], [6.234, 11.565], [10.901, 7.333], [-6.235, -11.566]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [145.271, 114.906], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 5, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.276, -1.183], [-1.183, -1.276]], "o": [[-1.183, -1.276], [-1.276, 1.182], [0, 0]], "v": [[-27.991, -34.837], [-32.443, -35.005], [-32.612, -30.554]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.277, 1.182], [1.182, 1.275]], "o": [[1.183, 1.276], [1.275, -1.182], [0, 0]], "v": [[27.992, 34.837], [32.444, 35.007], [32.613, 30.556]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-32.612, -30.554], [27.992, 34.837], [32.613, 30.556], [-27.991, -34.837]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [113.708, 139.285], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 5, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.732, -1.579], [-1.58, -0.732]], "o": [[-1.578, -0.731], [-0.73, 1.578], [0, 0]], "v": [[-8.063, -7.205], [-12.245, -5.67], [-10.71, -1.488]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.732, 1.579], [1.58, 0.731]], "o": [[1.579, 0.731], [0.73, -1.578], [0, 0]], "v": [[8.063, 7.205], [12.246, 5.67], [10.71, 1.489]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-10.71, -1.488], [8.063, 7.205], [10.71, 1.489], [-8.063, -7.205]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [101.613, 78.304], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 5, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.453, 0.956], [0.956, 1.453]], "o": [[0.957, 1.453], [1.453, -0.957], [0, 0]], "v": [[39.686, 19.79], [44.048, 20.69], [44.948, 16.326]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.956, -1.453], [-1.453, 0.957]], "o": [[-1.454, 0.956], [0.957, 1.453], [0, 0]], "v": [[-44.048, -2.117], [-44.948, 2.245], [-40.586, 3.144]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [29.665, -19.528], [0, 0], [-17.614, -26.759]], "o": [[-19.518, -29.649], [0, 0], [26.743, -17.604], [0, 0]], "v": [[44.948, 16.326], [-44.048, -2.117], [-40.586, 3.144], [39.686, 19.79]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [80.617, 167.009], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 5, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.057, -1.381], [-1.382, -1.057]], "o": [[-1.382, -1.057], [-1.057, 1.383], [0, 0]], "v": [[-10.774, 7.518], [-15.19, 8.106], [-14.601, 12.522]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0.711]], "o": [[0, 0], [0.442, -0.557], [0, 0]], "v": [[1.171, 8.257], [3.638, 10.215], [4.322, 8.257]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -0.698]], "o": [[0, 0], [-0.429, 0.552], [0, 0]], "v": [[1.297, 8.004], [-1.192, 6.075], [-1.852, 8.004]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.107, 0.832], [0.666, 0.509]], "o": [[0, 0], [0.515, -0.663], [-0.108, -0.831], [0, 0]], "v": [[13.015, -7.115], [15.504, -5.186], [16.139, -7.52], [14.93, -9.616]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [1.056, -1.382], [-1.382, -1.058]], "o": [[-1.382, -1.058], [-1.058, 1.381], [0, 0]], "v": [[6.867, -15.79], [2.451, -15.204], [3.037, -10.788]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [-4.463, 5.62], [0, 0], [2.912, 2.227]], "o": [[5.655, 4.325], [0, 0], [-2.339, 2.947], [0, 0]], "v": [[-14.601, 12.522], [3.638, 10.215], [-1.296, 6.298], [-10.774, 7.518]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [0.079, -0.282], [0.125, -0.208], [0.077, -0.088], [0.017, -0.019], [-0.007, 0.007], [-0.024, 0.025], [-0.046, 0.052], [-0.133, 0.221], [-0.087, 0.308], [0, 0.284], [0, 0], [-0.08, 0.282], [-0.125, 0.208], [-0.078, 0.088], [-0.018, 0.019], [0, 0], [0.025, -0.025], [0.046, -0.052], [0.132, -0.221], [0.086, -0.308], [0, -0.283]], "o": [[0, 0.252], [-0.08, 0.284], [-0.121, 0.201], [-0.039, 0.044], [-0.017, 0.017], [0.001, -0.001], [0.026, -0.026], [0.092, -0.105], [0.137, -0.227], [0.086, -0.307], [0, 0], [0, -0.252], [0.079, -0.283], [0.121, -0.201], [0.038, -0.044], [0, 0], [-0.002, 0.001], [-0.025, 0.026], [-0.092, 0.105], [-0.137, 0.228], [-0.086, 0.307], [0, 0]], "v": [[4.322, 8.257], [4.211, 9.064], [3.893, 9.803], [3.575, 10.238], [3.485, 10.335], [3.462, 10.358], [3.508, 10.311], [3.62, 10.191], [3.979, 9.699], [4.326, 8.895], [4.448, 8.004], [-1.852, 8.004], [-1.741, 7.197], [-1.423, 6.458], [-1.104, 6.022], [-1.016, 5.926], [-0.992, 5.903], [-1.039, 5.95], [-1.151, 6.07], [-1.51, 6.561], [-1.856, 7.366], [-1.977, 8.257]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.787, 9.934], [15.504, -5.186], [10.525, -9.045], [-1.192, 6.075]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[14.93, -9.616], [6.867, -15.79], [3.037, -10.788], [11.101, -4.613]], "c": true}, "ix": 2}, "nm": "Path 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [43.428, 51.463], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 11, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [10.458, 1.763], [1.764, -10.458]], "o": [[0, 0], [0, 0], [1.765, -10.583], [-10.584, -1.764], [0, 0]], "v": [[-22.049, 17.954], [16.001, 24.506], [20.284, -0.442], [4.535, -22.742], [-17.766, -6.993]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [123.853, 24.756], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-24.272, 12.61], [12.61, 24.272], [24.272, -12.61], [-12.61, -24.272]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [115.423, 61.488], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 2, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-27.51, 6.031], [4.456, 27.809], [27.51, -6.032], [-4.456, -27.809]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [156.107, 101.588], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 2, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-28.135, 2.032], [0.341, 28.207], [28.135, -2.032], [-0.341, -28.207]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [184.086, 146.344], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 2, "cix": 2, "bm": 0, "ix": 13, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-39.582, -1.043], [-21.704, 33.117], [39.582, 1.042], [21.704, -33.117]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [145.278, 173.092], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 14", "np": 2, "cix": 2, "bm": 0, "ix": 14, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [9.323, 5.04], [5.04, -9.324]], "o": [[0, 0], [0, 0], [5.166, -9.324], [-9.325, -5.165], [0, 0]], "v": [[-28.475, 13.544], [5.418, 32.066], [23.309, -0.693], [15.624, -26.9], [-10.583, -19.215]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [206.756, 112.449], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 15", "np": 2, "cix": 2, "bm": 0, "ix": 15, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [10.332, 3.024], [3.023, -10.332]], "o": [[0, 0], [0, 0], [2.898, -10.331], [-10.331, -2.898], [0, 0]], "v": [[-25.766, 22.931], [11.403, 33.515], [22.868, -6.678], [9.512, -30.617], [-14.426, -17.261]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [172.297, 62.239], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 16", "np": 2, "cix": 2, "bm": 0, "ix": 16, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-41.439, 11.352], [-15.519, 40.065], [41.439, -11.353], [15.519, -40.065]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [120.647, 138.824], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 17", "np": 2, "cix": 2, "bm": 0, "ix": 17, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-32.23, 15.064], [0.567, 35.574], [32.23, -15.065], [-0.567, -35.574]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [95.469, 100.603], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 18", "np": 2, "cix": 2, "bm": 0, "ix": 18, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [14.867, 13.859]], "o": [[0, 0], [0, 0], [13.734, -14.994], [0, 0]], "v": [[-30.302, 11.843], [-3.213, 36.79], [16.568, 15.246], [14.427, -36.79]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [41.766, 71.689], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 19", "np": 2, "cix": 2, "bm": 0, "ix": 19, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-23.756, 29.387], [12.494, 35.663], [23.756, -29.387], [-12.494, -35.663]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [24.155, 119.037], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 20", "np": 2, "cix": 2, "bm": 0, "ix": 20, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-22.428, 14.364], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [14.489, 22.427]], "v": [[23.057, 52.981], [80.763, 18.207], [-7.308, -67.345], [-80.763, -19.466], [-43.594, 38.492]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [81.013, 168.013], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 21", "np": 2, "cix": 2, "bm": 0, "ix": 21, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "Forearm Right", "parent": 20, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 60, "s": [-23.143]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 120, "s": [0]}, {"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 180, "s": [-23.143]}, {"t": 240, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [165.85, 180.5, 0], "ix": 2}, "a": {"a": 0, "k": [49.85, 136.1, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [10.6, 0], [0, 27], [-14.6, 8.4], [0, 0], [0, 0]], "o": [[0, 0], [-8, 5.8], [-27, 0], [0, -18.1], [0, 0], [0, 0], [0, 0]], "v": [[111.1, -10.25], [-33.7, 83.15], [-62.2, 92.35], [-111.1, 43.45], [-86.7, 1.15], [-86.6, 1.05], [58.1, -92.35]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [111.35, 92.6], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "Upper Arm Right", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -16.375, "ix": 10}, "p": {"a": 0, "k": [1024.763, 348.251, 0], "ix": 2}, "a": {"a": 0, "k": [48.95, 48.7, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 13.4], [-27, 0], [-8.899, -10.9]], "o": [[0, 0], [0, 0], [-8.699, -8.8], [0, -27], [15.199, 0], [0, 0]], "v": [[100.7, 41.6], [27.5, 106.2], [-86.601, -23], [-100.7, -57.3], [-51.8, -106.2], [-14.101, -88.4]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [100.95, 106.45], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 240, "st": 0, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 1, "nm": "Background", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "sw": 1600, "sh": 1200, "sc": "#ffffff", "ip": 240, "op": 240, "st": 0, "bm": 0, "hidden": 0}], "markers": []}