{"v": "5.6.5", "fr": 60, "ip": 0, "op": 210, "w": 1600, "h": 1200, "nm": "17_<PERSON><PERSON><PERSON>", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 5, "ty": 4, "nm": "<PERSON>l", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [553.25, 1036.5, 0], "ix": 2}, "a": {"a": 0, "k": [-246.75, 436.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.51, "y": 0}, "t": 34, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-207, 436.5], [-286.5, 436.5]], "c": false}]}, {"i": {"x": 0.49, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [{"i": [[28, 1.75], [21.25, -32.5]], "o": [[-28, -1.75], [-12.863, 19.673]], "v": [[-207, 436.5], [-285.25, 428]], "c": false}]}, {"i": {"x": 0.49, "y": 1}, "o": {"x": 0.51, "y": 0}, "t": 50, "s": [{"i": [[56, 3.5], [47, 1]], "o": [[-56, -3.5], [-47, -1]], "v": [[-207, 436.5], [-276, 381.5]], "c": false}]}, {"i": {"x": 0.49, "y": 1}, "o": {"x": 0.51, "y": 0}, "t": 57, "s": [{"i": [[22.4, 1.4], [6.3, 21]], "o": [[-22.4, -1.4], [-5.403, -18.011]], "v": [[-207, 436.5], [-282.3, 414.5]], "c": false}]}, {"i": {"x": 0.49, "y": 1}, "o": {"x": 0.51, "y": 0}, "t": 65, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-207, 436.5], [-286.5, 436.5]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.51, "y": 0}, "t": 139, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-207, 436.5], [-286.5, 436.5]], "c": false}]}, {"i": {"x": 0.49, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [{"i": [[28, 1.75], [21.25, -32.5]], "o": [[-28, -1.75], [-12.863, 19.673]], "v": [[-207, 436.5], [-285.25, 428]], "c": false}]}, {"i": {"x": 0.49, "y": 1}, "o": {"x": 0.51, "y": 0}, "t": 155, "s": [{"i": [[56, 3.5], [47, 1]], "o": [[-56, -3.5], [-47, -1]], "v": [[-207, 436.5], [-276, 381.5]], "c": false}]}, {"i": {"x": 0.49, "y": 1}, "o": {"x": 0.51, "y": 0}, "t": 162, "s": [{"i": [[22.4, 1.4], [6.3, 21]], "o": [[-22.4, -1.4], [-5.403, -18.011]], "v": [[-207, 436.5], [-282.3, 414.5]], "c": false}]}, {"t": 170, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-207, 436.5], [-286.5, 436.5]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.400000035763, 0, 1, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 41.2, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.400000029919, 0, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 210, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Bubble", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [575.301, 362.1, 0], "ix": 2}, "a": {"a": 0, "k": [41.25, 163.35, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.49, 0.49, 0.49], "y": [1, 1, 1]}, "o": {"x": [0.51, 0.51, 0.51], "y": [0, 0, 0]}, "t": 53, "s": [0, 0, 100]}, {"i": {"x": [0.49, 0.49, 0.49], "y": [1, 1, 1]}, "o": {"x": [0.51, 0.51, 0.51], "y": [0, 0, 0]}, "t": 66, "s": [110, 110, 100]}, {"i": {"x": [0, 0, 0], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 73, "s": [100, 100, 100]}, {"i": {"x": [0, 0, 0], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 185, "s": [100, 100, 100]}, {"t": 199, "s": [0, 0, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.694, -1.194], [-1.195, 0.693]], "o": [[-1.194, 0.693], [0.693, 1.194], [0, 0]], "v": [[-15.327, 14.337], [-16.234, 17.755], [-12.817, 18.663]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[0.228, 8.2], [-1.022, 6.035], [-1.027, 6.038]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0.893], [0.773, 0.446]], "o": [[0, 0], [0.773, -0.447], [0, -0.893], [0, 0]], "v": [[14.428, 0], [15.678, 2.165], [16.928, 0], [15.678, -2.164]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[0.228, -8.2], [-1.027, -6.038], [-1.022, -6.035]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0.693, -1.194], [-1.194, -0.694]], "o": [[-1.195, -0.693], [-0.694, 1.195], [0, 0]], "v": [[-12.817, -18.663], [-16.234, -17.755], [-15.327, -14.337]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-12.817, 18.663], [1.483, 10.362], [-1.027, 6.038], [-15.327, 14.337]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.478, 10.365], [15.678, 2.165], [13.178, -2.164], [-1.022, 6.035]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[15.678, -2.164], [1.478, -10.365], [-1.022, -6.035], [13.178, 2.165]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.483, -10.362], [-12.817, -18.663], [-15.327, -14.337], [-1.027, -6.038]], "c": true}, "ix": 2}, "nm": "Path 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [138.021, 63.15], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 11, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.697, 1.192], [1.192, 0.697]], "o": [[1.192, 0.696], [0.697, -1.192], [0, 0]], "v": [[12.811, 18.659], [16.231, 17.762], [15.333, 14.342]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.007, 0.003]], "o": [[0, 0], [-0.006, -0.004], [0, 0]], "v": [[-0.128, 8.201], [1.134, 6.042], [1.116, 6.032]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -0.895], [-0.777, -0.446]], "o": [[0, 0], [-0.777, 0.445], [0, 0.896], [0, 0]], "v": [[-14.427, 0], [-15.672, -2.168], [-16.927, 0], [-15.672, 2.169]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.006, 0.003]], "o": [[0, 0], [0.007, -0.003], [0, 0]], "v": [[-0.128, -8.199], [1.116, -6.031], [1.134, -6.041]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0.697, 1.192], [1.192, -0.697]], "o": [[1.192, -0.697], [-0.697, -1.192], [0, 0]], "v": [[15.333, -14.341], [16.231, -17.761], [12.811, -18.658]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[15.333, 14.342], [1.134, 6.042], [-1.389, 10.359], [12.811, 18.659]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.116, 6.032], [-13.185, -2.168], [-15.672, 2.169], [-1.371, 10.369]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-13.185, 2.169], [1.116, -6.031], [-1.371, -10.368], [-15.672, -2.168]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.134, -6.041], [15.333, -14.341], [12.811, -18.658], [-1.389, -10.358]], "c": true}, "ix": 2}, "nm": "Path 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.576, 63.15], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 11, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.262, 0.56], [0.56, -1.263]], "o": [[0.561, -1.262], [-1.261, -0.56], [0, 0]], "v": [[15.335, -28.386], [14.063, -31.685], [10.765, -30.414]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.262, -0.56], [-0.56, 1.262]], "o": [[-0.56, 1.263], [1.262, 0.56], [0, 0]], "v": [[-15.336, 28.385], [-14.065, 31.685], [-10.766, 30.414]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[10.765, -30.414], [-15.336, 28.385], [-10.766, 30.414], [15.335, -28.386]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [101.3, 63.15], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 5, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [21.1, 0], [0, 0]], "o": [[0, 21.2], [0, 0], [0, 0]], "v": [[19.15, -19.15], [-19.15, 19.15], [-19.15, -19.15]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.889999988032, 0.823999980852, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Pink').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [61.501, 144.3], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[12.7, 0], [0, 0], [0, 12.7], [0, 0], [-12.7, 0], [0, 0], [0, -12.7], [0, 0]], "o": [[0, 0], [-12.7, 0], [0, 0], [0, -12.7], [0, 0], [12.7, 0], [0, 0], [0, 12.8]], "v": [[84, 62.45], [-84, 62.45], [-107, 39.45], [-107, -39.45], [-84, -62.45], [84, -62.45], [107, -39.45], [107, 39.35]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.889999988032, 0.823999980852, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Pink').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [107.25, 62.7], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 210, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "<PERSON><PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [980.849, 534.75, 0], "ix": 2}, "a": {"a": 0, "k": [155.7, 59.7, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[16.25, -22], [50.95, 23.2], [-50.95, -23.2]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [197.501, 25.85], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.5, -31.7], [31.601, -1.5], [3.301, 0.4], [0, 0], [0, 0], [0, 0], [-0.9, 0.1]], "o": [[1.5, 31.7], [-3.5, 0.2], [0, 0], [0, 0], [0, 0], [0.8, -0.1], [31.6, -1.5]], "v": [[153.95, -0.75], [99.349, 59.25], [89.149, 58.85], [-155.45, 27.35], [-144.25, -59.45], [91.45, -55.15], [93.95, -55.35]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [155.7, 59.7], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 210, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Finger 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 13, "s": [9.866]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [9.866]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 28, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33, "s": [9.866]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 43, "s": [9.866]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 48, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 53, "s": [9.866]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 63, "s": [9.866]}, {"t": 68, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [826.879, 544.466, 0], "ix": 2}, "a": {"a": 0, "k": [168.671, 55.229, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.402, 1.321], [1.32, 0.402]], "o": [[1.321, 0.402], [0.401, -1.321], [0, 0]], "v": [[56.697, -19.375], [59.817, -21.038], [58.153, -24.159]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.627, -0.428]], "o": [[0, 0], [-0.726, -0.22], [0, 0]], "v": [[12.725, -35.366], [13.452, -37.759], [11.312, -37.43]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.777, -1.139]], "o": [[0, 0], [-1.137, 0.779], [0, 0]], "v": [[-57.375, 12.634], [-58.788, 10.57], [-59.441, 14.041]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-2.875, 22.833], [-4.29, 20.772], [-4.291, 20.772]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0.782, 1.139], [1.138, -0.781]], "o": [[1.139, -0.781], [-0.781, -1.138], [0, 0]], "v": [[20.539, 9.794], [21.186, 6.319], [17.71, 5.673]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[58.153, -24.159], [13.452, -37.759], [11.997, -32.975], [56.697, -19.375]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[11.312, -37.43], [-58.788, 10.57], [-55.963, 14.697], [14.137, -33.303]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [-19.042, 13.084], [0, 0], [11.42, 16.757]], "o": [[12.979, 19.043], [0, 0], [-16.759, 11.516], [0, 0]], "v": [[-59.441, 14.041], [-1.459, 24.895], [-4.291, 20.772], [-55.309, 11.225]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.461, 24.895], [20.539, 9.794], [17.71, 5.673], [-4.29, 20.772]], "c": true}, "ix": 2}, "nm": "Path 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [60.468, 38.229], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 11, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.12, -0.808], [-0.807, -1.12]], "o": [[-0.808, -1.12], [-1.12, 0.807], [0, 0]], "v": [[12.035, -13.531], [8.545, -14.097], [7.979, -10.607]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.426, 0.307]], "o": [[0, 0], [0.525, 0], [0, 0]], "v": [[7.907, 1.231], [7.907, 3.731], [9.369, 3.259]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.429, -0.315]], "o": [[0, 0], [-0.532, 0], [0, 0]], "v": [[7.707, 1.332], [7.707, -1.168], [6.229, -0.685]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.657, -0.103], [-0.536, 0.393]], "o": [[0, 0], [0.391, 0.539], [0.657, 0.103], [0, 0]], "v": [[-7.293, 12.331], [-9.317, 13.798], [-7.68, 14.801], [-5.815, 14.348]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [1.117, -0.811], [-0.81, -1.118]], "o": [[-0.811, -1.119], [-1.118, 0.81], [0, 0]], "v": [[-11.069, 2.864], [-14.56, 2.307], [-15.117, 5.798]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [3.158, -2.277], [0, 0], [3.892, 5.397]], "o": [[2.308, 3.202], [0, 0], [5.442, -3.924], [0, 0]], "v": [[7.979, -10.607], [6.445, -0.797], [9.369, 3.259], [12.035, -13.531]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [0.243, -0.067], [0.181, -0.109], [0.083, -0.073], [0.021, -0.02], [0.001, -0.001], [-0.013, 0.013], [-0.035, 0.03], [-0.16, 0.096], [-0.225, 0.063], [-0.2, 0], [0, 0], [-0.243, 0.068], [-0.181, 0.108], [-0.083, 0.073], [-0.021, 0.021], [-0.001, 0.001], [0.014, -0.013], [0.035, -0.031], [0.16, -0.096], [0.225, -0.063], [0.2, 0]], "o": [[-0.225, 0], [-0.245, 0.069], [-0.175, 0.105], [-0.041, 0.036], [-0.02, 0.019], [-0.005, 0.005], [0.015, -0.014], [0.069, -0.062], [0.165, -0.098], [0.224, -0.063], [0, 0], [0.225, 0], [0.245, -0.069], [0.175, -0.105], [0.041, -0.037], [0.02, -0.019], [0.005, -0.006], [-0.014, 0.014], [-0.07, 0.061], [-0.165, 0.099], [-0.224, 0.063], [0, 0]], "v": [[7.907, -1.269], [7.2, -1.173], [6.561, -0.897], [6.172, -0.612], [6.077, -0.524], [6.039, -0.487], [6.058, -0.505], [6.134, -0.574], [6.48, -0.829], [7.066, -1.081], [7.707, -1.168], [7.707, 3.831], [8.414, 3.734], [9.053, 3.46], [9.442, 3.174], [9.537, 3.085], [9.575, 3.049], [9.556, 3.067], [9.48, 3.137], [9.134, 3.39], [8.548, 3.642], [7.907, 3.731]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.229, -0.685], [-8.771, 10.315], [-5.815, 14.348], [9.185, 3.348]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.269, 10.864], [-11.069, 2.864], [-15.117, 5.798], [-9.317, 13.798]], "c": true}, "ix": 2}, "nm": "Path 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [23.783, 40.531], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 11, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-17.9, 12.3], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [12.2, 17.9], [0, 0], [0, 0]], "v": [[33, 30.25], [78.7, 30.05], [84.5, -5.25], [-14.4, -35.25], [-84.5, 12.75], [-30, 22.95], [-8, 7.85]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [87.593, 38.112], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 210, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Finger 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 13, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [9.866]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 28, "s": [9.866]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [9.866]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 43, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 48, "s": [9.866]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 53, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58, "s": [9.866]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 63, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 68, "s": [9.866]}, {"t": 73, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [829.35, 523.75, 0], "ix": 2}, "a": {"a": 0, "k": [202.1, 39.1, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.12, -0.808], [-0.807, -1.12]], "o": [[-0.808, -1.12], [-1.12, 0.807], [0, 0]], "v": [[12.035, -13.531], [8.545, -14.097], [7.979, -10.607]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.426, 0.307]], "o": [[0, 0], [0.525, 0], [0, 0]], "v": [[7.907, 1.231], [7.907, 3.731], [9.369, 3.259]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.43, -0.315]], "o": [[0, 0], [-0.532, 0], [0, 0]], "v": [[7.707, 1.332], [7.707, -1.168], [6.228, -0.685]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.657, -0.103], [-0.536, 0.393]], "o": [[0, 0], [0.391, 0.539], [0.657, 0.103], [0, 0]], "v": [[-7.293, 12.331], [-9.317, 13.798], [-7.68, 14.801], [-5.815, 14.348]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [1.117, -0.811], [-0.81, -1.118]], "o": [[-0.811, -1.119], [-1.118, 0.81], [0, 0]], "v": [[-11.069, 2.864], [-14.56, 2.307], [-15.117, 5.798]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [3.158, -2.277], [0, 0], [3.892, 5.397]], "o": [[2.308, 3.202], [0, 0], [5.442, -3.924], [0, 0]], "v": [[7.979, -10.607], [6.445, -0.797], [9.369, 3.259], [12.035, -13.531]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [0.243, -0.067], [0.181, -0.109], [0.083, -0.073], [0.021, -0.02], [0.001, -0.001], [0, 0], [-0.035, 0.03], [-0.16, 0.096], [-0.225, 0.063], [-0.2, 0], [0, 0], [-0.243, 0.068], [-0.181, 0.108], [-0.083, 0.073], [-0.021, 0.021], [-0.001, 0.001], [0.014, -0.013], [0.035, -0.031], [0.16, -0.096], [0.225, -0.063], [0.2, 0]], "o": [[-0.225, 0], [-0.245, 0.069], [-0.175, 0.105], [-0.041, 0.036], [-0.02, 0.019], [0, 0], [0.016, -0.014], [0.069, -0.062], [0.165, -0.098], [0.224, -0.063], [0, 0], [0.225, 0], [0.245, -0.069], [0.175, -0.105], [0.041, -0.037], [0.02, -0.019], [0.005, -0.006], [-0.014, 0.014], [-0.07, 0.061], [-0.165, 0.099], [-0.224, 0.063], [0, 0]], "v": [[7.907, -1.269], [7.2, -1.173], [6.561, -0.897], [6.172, -0.612], [6.077, -0.524], [6.039, -0.487], [6.057, -0.505], [6.134, -0.574], [6.48, -0.829], [7.066, -1.081], [7.707, -1.168], [7.707, 3.831], [8.414, 3.734], [9.053, 3.46], [9.442, 3.174], [9.537, 3.085], [9.575, 3.049], [9.556, 3.067], [9.48, 3.137], [9.134, 3.39], [8.548, 3.642], [7.907, 3.731]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.228, -0.685], [-8.772, 10.315], [-5.815, 14.348], [9.185, 3.348]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.269, 10.864], [-11.069, 2.864], [-15.117, 5.798], [-9.317, 13.798]], "c": true}, "ix": 2}, "nm": "Path 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.843, 44.619], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 11, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-17.9, 12.3], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [12.2, 17.9], [0, 0], [0, 0]], "v": [[93.15, 33.65], [103.85, -38.85], [-22.85, -38.45], [-23.05, -38.85], [-103.85, 16.35], [-49.35, 26.55], [-16.75, 4.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [104.1, 39.1], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 210, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Head", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 10, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 28, "s": [-14.609]}, {"i": {"x": [0], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 94, "s": [-14.609]}, {"t": 116, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [1118.681, 227.3, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [1096.681, 240.8, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [1096.681, 240.8, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 116, "s": [1118.681, 227.3, 0]}], "ix": 2}, "a": {"a": 0, "k": [43.17, 32.25, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.934, 0], [0, 1.933], [1.933, 0], [0, -1.933]], "o": [[1.933, 0], [0, -1.933], [-1.934, 0], [0, 1.933]], "v": [[0, 3.5], [3.5, 0], [0, -3.5], [-3.5, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [26.59, 35.15], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.274, -0.533], [-0.534, -1.274]], "o": [[-0.532, -1.274], [-1.274, 0.533], [0, 0]], "v": [[9.157, -7.365], [5.886, -8.705], [4.545, -5.434]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [1.274, -0.533], [-0.534, -1.274]], "o": [[-0.532, -1.274], [-1.274, 0.533], [0, 0]], "v": [[-6.343, -0.865], [-9.614, -2.205], [-10.955, 1.066]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [3.022, -1.265], [0, 0], [2.332, 5.57]], "o": [[1.268, 3.03], [0, 0], [5.578, -2.334], [0, 0]], "v": [[4.545, -5.434], [1.386, 2.295], [3.316, 6.907], [9.157, -7.365]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [1.266, 3.022], [0, 0], [-5.569, 2.332]], "o": [[-3.031, 1.269], [0, 0], [2.335, 5.577], [0, 0]], "v": [[1.386, 2.295], [-6.343, -0.865], [-10.955, 1.066], [3.316, 6.907]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [11.739, 53.449], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 6, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.5, -11.8], [11.701, -4.5], [0, 0], [4.5, 11.7], [-11.7, 4.5], [0, 0]], "o": [[4.5, 11.7], [0, 0], [-11.699, 4.5], [-4.5, -11.7], [0, 0], [11.799, -4.5]], "v": [[37.6, -14.4], [24.499, 15], [-8.201, 27.5], [-37.6, 14.4], [-24.501, -15], [8.2, -27.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.8, 0.322000002394, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Yellow').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [43.99, 32.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 210, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Hair 1", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [93.989, -11.95, 0], "to": [-0.133, 0, 0], "ti": [0.267, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [93.189, -11.95, 0], "to": [-0.267, 0, 0], "ti": [0.266, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [92.384, -11.95, 0], "to": [-0.266, 0, 0], "ti": [0.266, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [91.59, -11.95, 0], "to": [-0.266, 0, 0], "ti": [0.267, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17, "s": [90.789, -11.95, 0], "to": [-0.267, 0, 0], "ti": [0.269, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [89.989, -11.95, 0], "to": [-0.269, 0, 0], "ti": [0.258, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [89.172, -11.95, 0], "to": [-0.258, 0, 0], "ti": [0.224, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [88.443, -11.95, 0], "to": [-0.224, 0, 0], "ti": [0.183, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [87.828, -11.95, 0], "to": [-0.183, 0, 0], "ti": [0.136, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [87.347, -11.95, 0], "to": [-0.136, 0, 0], "ti": [0.087, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [87.011, -11.95, 0], "to": [-0.087, 0, 0], "ti": [0.037, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [86.826, -11.95, 0], "to": [-0.037, 0, 0], "ti": [-0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [86.79, -11.95, 0], "to": [0.011, 0, 0], "ti": [-0.056, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [86.894, -11.95, 0], "to": [0.056, 0, 0], "ti": [-0.096, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [87.127, -11.95, 0], "to": [0.096, 0, 0], "ti": [-0.128, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [87.468, -11.95, 0], "to": [0.128, 0, 0], "ti": [-0.153, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [87.897, -11.95, 0], "to": [0.153, 0, 0], "ti": [-0.17, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [88.388, -11.95, 0], "to": [0.17, 0, 0], "ti": [-0.178, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [88.918, -11.95, 0], "to": [0.178, 0, 0], "ti": [-0.179, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [89.459, -11.95, 0], "to": [0.179, 0, 0], "ti": [-0.171, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [89.989, -11.95, 0], "to": [0.171, 0, 0], "ti": [-0.156, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [90.484, -11.95, 0], "to": [0.156, 0, 0], "ti": [-0.136, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [90.926, -11.95, 0], "to": [0.136, 0, 0], "ti": [-0.111, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [91.299, -11.95, 0], "to": [0.111, 0, 0], "ti": [-0.083, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [91.591, -11.95, 0], "to": [0.083, 0, 0], "ti": [-0.053, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [91.795, -11.95, 0], "to": [0.053, 0, 0], "ti": [-0.022, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [91.907, -11.95, 0], "to": [0.022, 0, 0], "ti": [0.007, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [91.929, -11.95, 0], "to": [-0.007, 0, 0], "ti": [0.034, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [91.866, -11.95, 0], "to": [-0.034, 0, 0], "ti": [0.058, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [91.725, -11.95, 0], "to": [-0.058, 0, 0], "ti": [0.078, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [91.518, -11.95, 0], "to": [-0.078, 0, 0], "ti": [0.093, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [91.258, -11.95, 0], "to": [-0.093, 0, 0], "ti": [0.103, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [90.96, -11.95, 0], "to": [-0.103, 0, 0], "ti": [0.108, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [90.639, -11.95, 0], "to": [-0.108, 0, 0], "ti": [0.108, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47, "s": [90.31, -11.95, 0], "to": [-0.108, 0, 0], "ti": [0.104, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [89.989, -11.95, 0], "to": [-0.104, 0, 0], "ti": [0.095, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [89.688, -11.95, 0], "to": [-0.095, 0, 0], "ti": [0.082, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [89.42, -11.95, 0], "to": [-0.082, 0, 0], "ti": [0.067, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51, "s": [89.194, -11.95, 0], "to": [-0.067, 0, 0], "ti": [0.05, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [89.017, -11.95, 0], "to": [-0.05, 0, 0], "ti": [0.032, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [88.893, -11.95, 0], "to": [-0.032, 0, 0], "ti": [0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [88.825, -11.95, 0], "to": [-0.014, 0, 0], "ti": [-0.004, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [88.812, -11.95, 0], "to": [0.004, 0, 0], "ti": [-0.021, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [88.85, -11.95, 0], "to": [0.021, 0, 0], "ti": [-0.035, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [88.936, -11.95, 0], "to": [0.035, 0, 0], "ti": [-0.047, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [89.061, -11.95, 0], "to": [0.047, 0, 0], "ti": [-0.056, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59, "s": [89.219, -11.95, 0], "to": [0.056, 0, 0], "ti": [-0.063, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [89.4, -11.95, 0], "to": [0.063, 0, 0], "ti": [-0.066, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [89.595, -11.95, 0], "to": [0.066, 0, 0], "ti": [-0.066, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [89.794, -11.95, 0], "to": [0.066, 0, 0], "ti": [-0.063, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [89.989, -11.95, 0], "to": [0.063, 0, 0], "ti": [-0.057, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [90.171, -11.95, 0], "to": [0.057, 0, 0], "ti": [-0.05, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [90.334, -11.95, 0], "to": [0.05, 0, 0], "ti": [-0.041, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [90.471, -11.95, 0], "to": [0.041, 0, 0], "ti": [-0.03, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [90.578, -11.95, 0], "to": [0.03, 0, 0], "ti": [-0.019, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [90.653, -11.95, 0], "to": [0.019, 0, 0], "ti": [-0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [90.695, -11.95, 0], "to": [0.008, 0, 0], "ti": [0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [90.703, -11.95, 0], "to": [-0.003, 0, 0], "ti": [0.013, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [90.679, -11.95, 0], "to": [-0.013, 0, 0], "ti": [0.021, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [90.627, -11.95, 0], "to": [-0.021, 0, 0], "ti": [0.029, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [90.551, -11.95, 0], "to": [-0.029, 0, 0], "ti": [0.034, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [90.456, -11.95, 0], "to": [-0.034, 0, 0], "ti": [0.038, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [90.346, -11.95, 0], "to": [-0.038, 0, 0], "ti": [0.04, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [90.228, -11.95, 0], "to": [-0.04, 0, 0], "ti": [0.04, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [90.107, -11.95, 0], "to": [-0.04, 0, 0], "ti": [0.038, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [89.989, -11.95, 0], "to": [-0.038, 0, 0], "ti": [0.035, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [89.878, -11.95, 0], "to": [-0.035, 0, 0], "ti": [0.03, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [89.78, -11.95, 0], "to": [-0.03, 0, 0], "ti": [0.025, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [89.696, -11.95, 0], "to": [-0.025, 0, 0], "ti": [0.018, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [89.631, -11.95, 0], "to": [-0.018, 0, 0], "ti": [0.012, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [89.586, -11.95, 0], "to": [-0.012, 0, 0], "ti": [0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [89.561, -11.95, 0], "to": [-0.005, 0, 0], "ti": [-0.002, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [89.556, -11.95, 0], "to": [0.002, 0, 0], "ti": [-0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [89.57, -11.95, 0], "to": [0.008, 0, 0], "ti": [-0.013, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [89.601, -11.95, 0], "to": [0.013, 0, 0], "ti": [-0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [89.648, -11.95, 0], "to": [0.017, 0, 0], "ti": [-0.021, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [89.706, -11.95, 0], "to": [0.021, 0, 0], "ti": [-0.023, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [89.772, -11.95, 0], "to": [0.023, 0, 0], "ti": [-0.024, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [89.844, -11.95, 0], "to": [0.024, 0, 0], "ti": [-0.024, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [89.917, -11.95, 0], "to": [0.024, 0, 0], "ti": [-0.023, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [89.989, -11.95, 0], "to": [0.023, 0, 0], "ti": [-0.021, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [90.056, -11.95, 0], "to": [0.021, 0, 0], "ti": [-0.018, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [90.116, -11.95, 0], "to": [0.018, 0, 0], "ti": [-0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [90.166, -11.95, 0], "to": [0.015, 0, 0], "ti": [-0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [90.206, -11.95, 0], "to": [0.011, 0, 0], "ti": [-0.007, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [90.233, -11.95, 0], "to": [0.007, 0, 0], "ti": [-0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [90.248, -11.95, 0], "to": [0.003, 0, 0], "ti": [0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [90.251, -11.95, 0], "to": [-0.001, 0, 0], "ti": [0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [90.243, -11.95, 0], "to": [-0.005, 0, 0], "ti": [0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [90.224, -11.95, 0], "to": [-0.008, 0, 0], "ti": [0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [90.196, -11.95, 0], "to": [-0.011, 0, 0], "ti": [0.034, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [90.161, -11.95, 0], "to": [-0.034, 0, 0], "ti": [-0.067, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [89.989, -11.95, 0], "to": [0.067, 0, 0], "ti": [-0.191, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [90.563, -11.95, 0], "to": [0.191, 0, 0], "ti": [-0.191, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [91.135, -11.95, 0], "to": [0.191, 0, 0], "ti": [-0.19, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [91.707, -11.95, 0], "to": [0.19, 0, 0], "ti": [-0.19, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [92.274, -11.95, 0], "to": [0.19, 0, 0], "ti": [-0.19, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [92.844, -11.95, 0], "to": [0.19, 0, 0], "ti": [-0.191, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111, "s": [93.413, -11.95, 0], "to": [0.191, 0, 0], "ti": [-0.194, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [93.989, -11.95, 0], "to": [0.194, 0, 0], "ti": [-0.185, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [94.575, -11.95, 0], "to": [0.185, 0, 0], "ti": [-0.161, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [95.097, -11.95, 0], "to": [0.161, 0, 0], "ti": [-0.131, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [95.538, -11.95, 0], "to": [0.131, 0, 0], "ti": [-0.098, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [95.884, -11.95, 0], "to": [0.098, 0, 0], "ti": [-0.062, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117, "s": [96.125, -11.95, 0], "to": [0.062, 0, 0], "ti": [-0.026, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [96.257, -11.95, 0], "to": [0.026, 0, 0], "ti": [0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [96.283, -11.95, 0], "to": [-0.008, 0, 0], "ti": [0.04, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [96.208, -11.95, 0], "to": [-0.04, 0, 0], "ti": [0.069, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121, "s": [96.041, -11.95, 0], "to": [-0.069, 0, 0], "ti": [0.092, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [95.797, -11.95, 0], "to": [-0.092, 0, 0], "ti": [0.11, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [95.489, -11.95, 0], "to": [-0.11, 0, 0], "ti": [0.122, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [95.137, -11.95, 0], "to": [-0.122, 0, 0], "ti": [0.128, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [94.757, -11.95, 0], "to": [-0.128, 0, 0], "ti": [0.128, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126, "s": [94.369, -11.95, 0], "to": [-0.128, 0, 0], "ti": [0.123, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127, "s": [93.989, -11.95, 0], "to": [-0.123, 0, 0], "ti": [0.112, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128, "s": [93.633, -11.95, 0], "to": [-0.112, 0, 0], "ti": [0.097, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 129, "s": [93.316, -11.95, 0], "to": [-0.097, 0, 0], "ti": [0.079, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [93.049, -11.95, 0], "to": [-0.079, 0, 0], "ti": [0.059, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [92.84, -11.95, 0], "to": [-0.059, 0, 0], "ti": [0.038, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [92.693, -11.95, 0], "to": [-0.038, 0, 0], "ti": [0.016, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133, "s": [92.613, -11.95, 0], "to": [-0.016, 0, 0], "ti": [-0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [92.597, -11.95, 0], "to": [0.005, 0, 0], "ti": [-0.024, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [92.643, -11.95, 0], "to": [0.024, 0, 0], "ti": [-0.042, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136, "s": [92.744, -11.95, 0], "to": [0.042, 0, 0], "ti": [-0.056, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [92.892, -11.95, 0], "to": [0.056, 0, 0], "ti": [-0.067, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138, "s": [93.079, -11.95, 0], "to": [0.067, 0, 0], "ti": [-0.074, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [93.293, -11.95, 0], "to": [0.074, 0, 0], "ti": [-0.078, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [93.523, -11.95, 0], "to": [0.078, 0, 0], "ti": [-0.078, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [93.758, -11.95, 0], "to": [0.078, 0, 0], "ti": [-0.074, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [93.989, -11.95, 0], "to": [0.074, 0, 0], "ti": [-0.068, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143, "s": [94.204, -11.95, 0], "to": [0.068, 0, 0], "ti": [-0.059, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [94.397, -11.95, 0], "to": [0.059, 0, 0], "ti": [-0.048, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [94.559, -11.95, 0], "to": [0.048, 0, 0], "ti": [-0.036, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [94.686, -11.95, 0], "to": [0.036, 0, 0], "ti": [-0.023, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [94.774, -11.95, 0], "to": [0.023, 0, 0], "ti": [-0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148, "s": [94.823, -11.95, 0], "to": [0.01, 0, 0], "ti": [0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [94.833, -11.95, 0], "to": [-0.003, 0, 0], "ti": [0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [94.805, -11.95, 0], "to": [-0.015, 0, 0], "ti": [0.025, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [94.744, -11.95, 0], "to": [-0.025, 0, 0], "ti": [0.034, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [94.654, -11.95, 0], "to": [-0.034, 0, 0], "ti": [0.04, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153, "s": [94.541, -11.95, 0], "to": [-0.04, 0, 0], "ti": [0.045, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [94.411, -11.95, 0], "to": [-0.045, 0, 0], "ti": [0.047, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [94.271, -11.95, 0], "to": [-0.047, 0, 0], "ti": [0.047, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156, "s": [94.129, -11.95, 0], "to": [-0.047, 0, 0], "ti": [0.045, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157, "s": [93.989, -11.95, 0], "to": [-0.045, 0, 0], "ti": [0.041, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [93.858, -11.95, 0], "to": [-0.041, 0, 0], "ti": [0.036, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159, "s": [93.741, -11.95, 0], "to": [-0.036, 0, 0], "ti": [0.029, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [93.643, -11.95, 0], "to": [-0.029, 0, 0], "ti": [0.022, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [93.566, -11.95, 0], "to": [-0.022, 0, 0], "ti": [0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162, "s": [93.512, -11.95, 0], "to": [-0.014, 0, 0], "ti": [0.006, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [93.483, -11.95, 0], "to": [-0.006, 0, 0], "ti": [-0.002, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [93.477, -11.95, 0], "to": [0.002, 0, 0], "ti": [-0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [93.494, -11.95, 0], "to": [0.009, 0, 0], "ti": [-0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166, "s": [93.531, -11.95, 0], "to": [0.015, 0, 0], "ti": [-0.021, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [93.585, -11.95, 0], "to": [0.021, 0, 0], "ti": [-0.025, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168, "s": [93.654, -11.95, 0], "to": [0.025, 0, 0], "ti": [-0.027, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169, "s": [93.733, -11.95, 0], "to": [0.027, 0, 0], "ti": [-0.029, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [93.817, -11.95, 0], "to": [0.029, 0, 0], "ti": [-0.029, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171, "s": [93.904, -11.95, 0], "to": [0.029, 0, 0], "ti": [-0.027, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172, "s": [93.989, -11.95, 0], "to": [0.027, 0, 0], "ti": [-0.025, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [94.068, -11.95, 0], "to": [0.025, 0, 0], "ti": [-0.022, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [94.139, -11.95, 0], "to": [0.022, 0, 0], "ti": [-0.018, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [94.199, -11.95, 0], "to": [0.018, 0, 0], "ti": [-0.013, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176, "s": [94.245, -11.95, 0], "to": [0.013, 0, 0], "ti": [-0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177, "s": [94.278, -11.95, 0], "to": [0.008, 0, 0], "ti": [-0.004, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178, "s": [94.296, -11.95, 0], "to": [0.004, 0, 0], "ti": [0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [94.299, -11.95, 0], "to": [-0.001, 0, 0], "ti": [0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [94.289, -11.95, 0], "to": [-0.005, 0, 0], "ti": [0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181, "s": [94.267, -11.95, 0], "to": [-0.009, 0, 0], "ti": [0.012, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182, "s": [94.233, -11.95, 0], "to": [-0.012, 0, 0], "ti": [0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183, "s": [94.192, -11.95, 0], "to": [-0.015, 0, 0], "ti": [0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 184, "s": [94.144, -11.95, 0], "to": [-0.017, 0, 0], "ti": [0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [94.093, -11.95, 0], "to": [-0.017, 0, 0], "ti": [0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 186, "s": [94.04, -11.95, 0], "to": [-0.017, 0, 0], "ti": [0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187, "s": [93.989, -11.95, 0], "to": [-0.017, 0, 0], "ti": [0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188, "s": [93.941, -11.95, 0], "to": [-0.015, 0, 0], "ti": [0.013, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 189, "s": [93.898, -11.95, 0], "to": [-0.013, 0, 0], "ti": [0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [93.862, -11.95, 0], "to": [-0.011, 0, 0], "ti": [0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191, "s": [93.833, -11.95, 0], "to": [-0.008, 0, 0], "ti": [0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192, "s": [93.813, -11.95, 0], "to": [-0.005, 0, 0], "ti": [0.002, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193, "s": [93.803, -11.95, 0], "to": [-0.002, 0, 0], "ti": [-0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [93.8, -11.95, 0], "to": [0.001, 0, 0], "ti": [-0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [93.807, -11.95, 0], "to": [0.003, 0, 0], "ti": [-0.006, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [93.82, -11.95, 0], "to": [0.006, 0, 0], "ti": [-0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [93.84, -11.95, 0], "to": [0.008, 0, 0], "ti": [-0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198, "s": [93.866, -11.95, 0], "to": [0.009, 0, 0], "ti": [-0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199, "s": [93.895, -11.95, 0], "to": [0.01, 0, 0], "ti": [-0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [93.926, -11.95, 0], "to": [0.011, 0, 0], "ti": [-0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 201, "s": [93.958, -11.95, 0], "to": [0.011, 0, 0], "ti": [-0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [93.989, -11.95, 0], "to": [0.01, 0, 0], "ti": [-0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [94.018, -11.95, 0], "to": [0.009, 0, 0], "ti": [-0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 204, "s": [94.044, -11.95, 0], "to": [0.008, 0, 0], "ti": [-0.007, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [94.066, -11.95, 0], "to": [0.007, 0, 0], "ti": [-0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [94.083, -11.95, 0], "to": [0.005, 0, 0], "ti": [-0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [94.095, -11.95, 0], "to": [0.003, 0, 0], "ti": [-0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208, "s": [94.102, -11.95, 0], "to": [0.001, 0, 0], "ti": [0, 0, 0]}, {"t": 209, "s": [94.103, -11.95, 0]}], "ix": 2}, "a": {"a": 0, "k": [17.75, 17.75, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-9.665, 0], [0, 9.665], [9.665, 0], [0, -9.665]], "o": [[9.665, 0], [0, -9.665], [-9.665, 0], [0, 9.665]], "v": [[0, 17.5], [17.5, 0], [0, -17.5], [-17.5, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [17.75, 17.75], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 210, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Hair 2", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [90.689, -44.15, 0], "to": [-0.133, 0, 0], "ti": [0.267, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17, "s": [89.889, -44.15, 0], "to": [-0.267, 0, 0], "ti": [0.266, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [89.085, -44.15, 0], "to": [-0.266, 0, 0], "ti": [0.266, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [88.291, -44.15, 0], "to": [-0.266, 0, 0], "ti": [0.267, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [87.49, -44.15, 0], "to": [-0.267, 0, 0], "ti": [0.269, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [86.689, -44.15, 0], "to": [-0.269, 0, 0], "ti": [0.258, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [85.873, -44.15, 0], "to": [-0.258, 0, 0], "ti": [0.224, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [85.144, -44.15, 0], "to": [-0.224, 0, 0], "ti": [0.183, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [84.529, -44.15, 0], "to": [-0.183, 0, 0], "ti": [0.136, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [84.047, -44.15, 0], "to": [-0.136, 0, 0], "ti": [0.087, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [83.712, -44.15, 0], "to": [-0.087, 0, 0], "ti": [0.037, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [83.526, -44.15, 0], "to": [-0.037, 0, 0], "ti": [-0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [83.49, -44.15, 0], "to": [0.011, 0, 0], "ti": [-0.056, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [83.595, -44.15, 0], "to": [0.056, 0, 0], "ti": [-0.096, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [83.827, -44.15, 0], "to": [0.096, 0, 0], "ti": [-0.128, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [84.169, -44.15, 0], "to": [0.128, 0, 0], "ti": [-0.153, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [84.597, -44.15, 0], "to": [0.153, 0, 0], "ti": [-0.17, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [85.089, -44.15, 0], "to": [0.17, 0, 0], "ti": [-0.178, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [85.618, -44.15, 0], "to": [0.178, 0, 0], "ti": [-0.179, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [86.16, -44.15, 0], "to": [0.179, 0, 0], "ti": [-0.171, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [86.689, -44.15, 0], "to": [0.171, 0, 0], "ti": [-0.156, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [87.185, -44.15, 0], "to": [0.156, 0, 0], "ti": [-0.136, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [87.627, -44.15, 0], "to": [0.136, 0, 0], "ti": [-0.111, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [88, -44.15, 0], "to": [0.111, 0, 0], "ti": [-0.083, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [88.292, -44.15, 0], "to": [0.083, 0, 0], "ti": [-0.053, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [88.496, -44.15, 0], "to": [0.053, 0, 0], "ti": [-0.022, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [88.608, -44.15, 0], "to": [0.022, 0, 0], "ti": [0.007, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [88.63, -44.15, 0], "to": [-0.007, 0, 0], "ti": [0.034, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [88.566, -44.15, 0], "to": [-0.034, 0, 0], "ti": [0.058, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [88.425, -44.15, 0], "to": [-0.058, 0, 0], "ti": [0.078, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [88.218, -44.15, 0], "to": [-0.078, 0, 0], "ti": [0.093, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47, "s": [87.958, -44.15, 0], "to": [-0.093, 0, 0], "ti": [0.103, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [87.66, -44.15, 0], "to": [-0.103, 0, 0], "ti": [0.108, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [87.339, -44.15, 0], "to": [-0.108, 0, 0], "ti": [0.108, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [87.011, -44.15, 0], "to": [-0.108, 0, 0], "ti": [0.104, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51, "s": [86.689, -44.15, 0], "to": [-0.104, 0, 0], "ti": [0.095, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [86.389, -44.15, 0], "to": [-0.095, 0, 0], "ti": [0.082, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [86.121, -44.15, 0], "to": [-0.082, 0, 0], "ti": [0.067, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [85.895, -44.15, 0], "to": [-0.067, 0, 0], "ti": [0.05, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [85.718, -44.15, 0], "to": [-0.05, 0, 0], "ti": [0.032, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [85.594, -44.15, 0], "to": [-0.032, 0, 0], "ti": [0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [85.526, -44.15, 0], "to": [-0.014, 0, 0], "ti": [-0.004, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [85.513, -44.15, 0], "to": [0.004, 0, 0], "ti": [-0.021, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59, "s": [85.551, -44.15, 0], "to": [0.021, 0, 0], "ti": [-0.035, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [85.637, -44.15, 0], "to": [0.035, 0, 0], "ti": [-0.047, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [85.762, -44.15, 0], "to": [0.047, 0, 0], "ti": [-0.056, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [85.92, -44.15, 0], "to": [0.056, 0, 0], "ti": [-0.063, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [86.101, -44.15, 0], "to": [0.063, 0, 0], "ti": [-0.066, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [86.295, -44.15, 0], "to": [0.066, 0, 0], "ti": [-0.066, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [86.495, -44.15, 0], "to": [0.066, 0, 0], "ti": [-0.063, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [86.689, -44.15, 0], "to": [0.063, 0, 0], "ti": [-0.057, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [86.872, -44.15, 0], "to": [0.057, 0, 0], "ti": [-0.05, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [87.034, -44.15, 0], "to": [0.05, 0, 0], "ti": [-0.041, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [87.172, -44.15, 0], "to": [0.041, 0, 0], "ti": [-0.03, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [87.279, -44.15, 0], "to": [0.03, 0, 0], "ti": [-0.019, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [87.354, -44.15, 0], "to": [0.019, 0, 0], "ti": [-0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [87.395, -44.15, 0], "to": [0.008, 0, 0], "ti": [0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [87.403, -44.15, 0], "to": [-0.003, 0, 0], "ti": [0.013, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [87.38, -44.15, 0], "to": [-0.013, 0, 0], "ti": [0.021, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [87.328, -44.15, 0], "to": [-0.021, 0, 0], "ti": [0.029, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [87.252, -44.15, 0], "to": [-0.029, 0, 0], "ti": [0.034, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [87.156, -44.15, 0], "to": [-0.034, 0, 0], "ti": [0.038, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [87.047, -44.15, 0], "to": [-0.038, 0, 0], "ti": [0.04, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [86.929, -44.15, 0], "to": [-0.04, 0, 0], "ti": [0.04, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [86.808, -44.15, 0], "to": [-0.04, 0, 0], "ti": [0.038, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [86.689, -44.15, 0], "to": [-0.038, 0, 0], "ti": [0.035, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [86.579, -44.15, 0], "to": [-0.035, 0, 0], "ti": [0.03, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [86.48, -44.15, 0], "to": [-0.03, 0, 0], "ti": [0.025, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [86.397, -44.15, 0], "to": [-0.025, 0, 0], "ti": [0.018, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [86.332, -44.15, 0], "to": [-0.018, 0, 0], "ti": [0.012, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [86.286, -44.15, 0], "to": [-0.012, 0, 0], "ti": [0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [86.261, -44.15, 0], "to": [-0.005, 0, 0], "ti": [-0.002, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [86.257, -44.15, 0], "to": [0.002, 0, 0], "ti": [-0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [86.271, -44.15, 0], "to": [0.008, 0, 0], "ti": [-0.013, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [86.302, -44.15, 0], "to": [0.013, 0, 0], "ti": [-0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [86.348, -44.15, 0], "to": [0.017, 0, 0], "ti": [-0.021, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [86.406, -44.15, 0], "to": [0.021, 0, 0], "ti": [-0.023, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [86.473, -44.15, 0], "to": [0.023, 0, 0], "ti": [-0.024, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [86.545, -44.15, 0], "to": [0.024, 0, 0], "ti": [-0.024, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [86.618, -44.15, 0], "to": [0.024, 0, 0], "ti": [-0.023, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [86.689, -44.15, 0], "to": [0.023, 0, 0], "ti": [-0.021, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [86.757, -44.15, 0], "to": [0.021, 0, 0], "ti": [-0.018, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [86.816, -44.15, 0], "to": [0.018, 0, 0], "ti": [-0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [86.867, -44.15, 0], "to": [0.015, 0, 0], "ti": [-0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [86.906, -44.15, 0], "to": [0.011, 0, 0], "ti": [-0.007, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [86.934, -44.15, 0], "to": [0.007, 0, 0], "ti": [-0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [86.949, -44.15, 0], "to": [0.003, 0, 0], "ti": [0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [86.952, -44.15, 0], "to": [-0.001, 0, 0], "ti": [0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [86.943, -44.15, 0], "to": [-0.005, 0, 0], "ti": [0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [86.924, -44.15, 0], "to": [-0.008, 0, 0], "ti": [0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [86.896, -44.15, 0], "to": [-0.011, 0, 0], "ti": [0.034, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [86.861, -44.15, 0], "to": [-0.034, 0, 0], "ti": [-0.067, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [86.689, -44.15, 0], "to": [0.067, 0, 0], "ti": [-0.191, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [87.263, -44.15, 0], "to": [0.191, 0, 0], "ti": [-0.191, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [87.836, -44.15, 0], "to": [0.191, 0, 0], "ti": [-0.19, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111, "s": [88.408, -44.15, 0], "to": [0.19, 0, 0], "ti": [-0.19, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [88.974, -44.15, 0], "to": [0.19, 0, 0], "ti": [-0.19, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [89.545, -44.15, 0], "to": [0.19, 0, 0], "ti": [-0.191, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [90.113, -44.15, 0], "to": [0.191, 0, 0], "ti": [-0.194, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [90.689, -44.15, 0], "to": [0.194, 0, 0], "ti": [-0.185, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [91.275, -44.15, 0], "to": [0.185, 0, 0], "ti": [-0.161, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117, "s": [91.798, -44.15, 0], "to": [0.161, 0, 0], "ti": [-0.131, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [92.239, -44.15, 0], "to": [0.131, 0, 0], "ti": [-0.098, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [92.584, -44.15, 0], "to": [0.098, 0, 0], "ti": [-0.062, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [92.825, -44.15, 0], "to": [0.062, 0, 0], "ti": [-0.026, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121, "s": [92.958, -44.15, 0], "to": [0.026, 0, 0], "ti": [0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [92.984, -44.15, 0], "to": [-0.008, 0, 0], "ti": [0.04, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [92.909, -44.15, 0], "to": [-0.04, 0, 0], "ti": [0.069, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [92.742, -44.15, 0], "to": [-0.069, 0, 0], "ti": [0.092, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [92.497, -44.15, 0], "to": [-0.092, 0, 0], "ti": [0.11, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126, "s": [92.19, -44.15, 0], "to": [-0.11, 0, 0], "ti": [0.122, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127, "s": [91.837, -44.15, 0], "to": [-0.122, 0, 0], "ti": [0.128, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128, "s": [91.458, -44.15, 0], "to": [-0.128, 0, 0], "ti": [0.128, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 129, "s": [91.069, -44.15, 0], "to": [-0.128, 0, 0], "ti": [0.123, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [90.689, -44.15, 0], "to": [-0.123, 0, 0], "ti": [0.112, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [90.334, -44.15, 0], "to": [-0.112, 0, 0], "ti": [0.097, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [90.017, -44.15, 0], "to": [-0.097, 0, 0], "ti": [0.079, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133, "s": [89.75, -44.15, 0], "to": [-0.079, 0, 0], "ti": [0.059, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 134, "s": [89.54, -44.15, 0], "to": [-0.059, 0, 0], "ti": [0.038, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 135, "s": [89.394, -44.15, 0], "to": [-0.038, 0, 0], "ti": [0.016, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136, "s": [89.314, -44.15, 0], "to": [-0.016, 0, 0], "ti": [-0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 137, "s": [89.298, -44.15, 0], "to": [0.005, 0, 0], "ti": [-0.024, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 138, "s": [89.343, -44.15, 0], "to": [0.024, 0, 0], "ti": [-0.042, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 139, "s": [89.444, -44.15, 0], "to": [0.042, 0, 0], "ti": [-0.056, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 140, "s": [89.593, -44.15, 0], "to": [0.056, 0, 0], "ti": [-0.067, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 141, "s": [89.779, -44.15, 0], "to": [0.067, 0, 0], "ti": [-0.074, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 142, "s": [89.993, -44.15, 0], "to": [0.074, 0, 0], "ti": [-0.078, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 143, "s": [90.224, -44.15, 0], "to": [0.078, 0, 0], "ti": [-0.078, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 144, "s": [90.459, -44.15, 0], "to": [0.078, 0, 0], "ti": [-0.074, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 145, "s": [90.689, -44.15, 0], "to": [0.074, 0, 0], "ti": [-0.068, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 146, "s": [90.905, -44.15, 0], "to": [0.068, 0, 0], "ti": [-0.059, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 147, "s": [91.097, -44.15, 0], "to": [0.059, 0, 0], "ti": [-0.048, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 148, "s": [91.26, -44.15, 0], "to": [0.048, 0, 0], "ti": [-0.036, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 149, "s": [91.387, -44.15, 0], "to": [0.036, 0, 0], "ti": [-0.023, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 150, "s": [91.475, -44.15, 0], "to": [0.023, 0, 0], "ti": [-0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 151, "s": [91.524, -44.15, 0], "to": [0.01, 0, 0], "ti": [0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 152, "s": [91.534, -44.15, 0], "to": [-0.003, 0, 0], "ti": [0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 153, "s": [91.506, -44.15, 0], "to": [-0.015, 0, 0], "ti": [0.025, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 154, "s": [91.445, -44.15, 0], "to": [-0.025, 0, 0], "ti": [0.034, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 155, "s": [91.355, -44.15, 0], "to": [-0.034, 0, 0], "ti": [0.04, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 156, "s": [91.242, -44.15, 0], "to": [-0.04, 0, 0], "ti": [0.045, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 157, "s": [91.112, -44.15, 0], "to": [-0.045, 0, 0], "ti": [0.047, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 158, "s": [90.972, -44.15, 0], "to": [-0.047, 0, 0], "ti": [0.047, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 159, "s": [90.829, -44.15, 0], "to": [-0.047, 0, 0], "ti": [0.045, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 160, "s": [90.689, -44.15, 0], "to": [-0.045, 0, 0], "ti": [0.041, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 161, "s": [90.559, -44.15, 0], "to": [-0.041, 0, 0], "ti": [0.036, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 162, "s": [90.442, -44.15, 0], "to": [-0.036, 0, 0], "ti": [0.029, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 163, "s": [90.344, -44.15, 0], "to": [-0.029, 0, 0], "ti": [0.022, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 164, "s": [90.267, -44.15, 0], "to": [-0.022, 0, 0], "ti": [0.014, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 165, "s": [90.213, -44.15, 0], "to": [-0.014, 0, 0], "ti": [0.006, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 166, "s": [90.183, -44.15, 0], "to": [-0.006, 0, 0], "ti": [-0.002, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 167, "s": [90.178, -44.15, 0], "to": [0.002, 0, 0], "ti": [-0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 168, "s": [90.194, -44.15, 0], "to": [0.009, 0, 0], "ti": [-0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 169, "s": [90.231, -44.15, 0], "to": [0.015, 0, 0], "ti": [-0.021, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 170, "s": [90.286, -44.15, 0], "to": [0.021, 0, 0], "ti": [-0.025, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 171, "s": [90.355, -44.15, 0], "to": [0.025, 0, 0], "ti": [-0.027, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 172, "s": [90.433, -44.15, 0], "to": [0.027, 0, 0], "ti": [-0.029, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 173, "s": [90.518, -44.15, 0], "to": [0.029, 0, 0], "ti": [-0.029, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 174, "s": [90.605, -44.15, 0], "to": [0.029, 0, 0], "ti": [-0.027, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 175, "s": [90.689, -44.15, 0], "to": [0.027, 0, 0], "ti": [-0.025, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 176, "s": [90.769, -44.15, 0], "to": [0.025, 0, 0], "ti": [-0.022, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 177, "s": [90.84, -44.15, 0], "to": [0.022, 0, 0], "ti": [-0.018, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 178, "s": [90.899, -44.15, 0], "to": [0.018, 0, 0], "ti": [-0.013, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 179, "s": [90.946, -44.15, 0], "to": [0.013, 0, 0], "ti": [-0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [90.979, -44.15, 0], "to": [0.008, 0, 0], "ti": [-0.004, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 181, "s": [90.997, -44.15, 0], "to": [0.004, 0, 0], "ti": [0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 182, "s": [91, -44.15, 0], "to": [-0.001, 0, 0], "ti": [0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 183, "s": [90.99, -44.15, 0], "to": [-0.005, 0, 0], "ti": [0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 184, "s": [90.967, -44.15, 0], "to": [-0.009, 0, 0], "ti": [0.012, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 185, "s": [90.934, -44.15, 0], "to": [-0.012, 0, 0], "ti": [0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 186, "s": [90.893, -44.15, 0], "to": [-0.015, 0, 0], "ti": [0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 187, "s": [90.845, -44.15, 0], "to": [-0.017, 0, 0], "ti": [0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 188, "s": [90.793, -44.15, 0], "to": [-0.017, 0, 0], "ti": [0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 189, "s": [90.741, -44.15, 0], "to": [-0.017, 0, 0], "ti": [0.017, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 190, "s": [90.689, -44.15, 0], "to": [-0.017, 0, 0], "ti": [0.015, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 191, "s": [90.641, -44.15, 0], "to": [-0.015, 0, 0], "ti": [0.013, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 192, "s": [90.599, -44.15, 0], "to": [-0.013, 0, 0], "ti": [0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 193, "s": [90.562, -44.15, 0], "to": [-0.011, 0, 0], "ti": [0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 194, "s": [90.534, -44.15, 0], "to": [-0.008, 0, 0], "ti": [0.005, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 195, "s": [90.514, -44.15, 0], "to": [-0.005, 0, 0], "ti": [0.002, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 196, "s": [90.503, -44.15, 0], "to": [-0.002, 0, 0], "ti": [-0.001, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 197, "s": [90.501, -44.15, 0], "to": [0.001, 0, 0], "ti": [-0.003, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 198, "s": [90.507, -44.15, 0], "to": [0.003, 0, 0], "ti": [-0.006, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 199, "s": [90.521, -44.15, 0], "to": [0.006, 0, 0], "ti": [-0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 200, "s": [90.541, -44.15, 0], "to": [0.008, 0, 0], "ti": [-0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 201, "s": [90.566, -44.15, 0], "to": [0.009, 0, 0], "ti": [-0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202, "s": [90.595, -44.15, 0], "to": [0.01, 0, 0], "ti": [-0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 203, "s": [90.626, -44.15, 0], "to": [0.011, 0, 0], "ti": [-0.011, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 204, "s": [90.658, -44.15, 0], "to": [0.011, 0, 0], "ti": [-0.01, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 205, "s": [90.689, -44.15, 0], "to": [0.01, 0, 0], "ti": [-0.009, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 206, "s": [90.719, -44.15, 0], "to": [0.009, 0, 0], "ti": [-0.008, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 207, "s": [90.745, -44.15, 0], "to": [0.008, 0, 0], "ti": [-0.007, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 208, "s": [90.767, -44.15, 0], "to": [0.007, 0, 0], "ti": [-0.003, 0, 0]}, {"t": 209, "s": [90.784, -44.15, 0]}], "ix": 2}, "a": {"a": 0, "k": [9.15, 9.15, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.916, 0], [0, 4.916], [4.915, 0], [0, -4.915]], "o": [[4.915, 0], [0, -4.915], [-4.916, 0], [0, 4.916]], "v": [[0.001, 8.9], [8.899, 0], [0.001, -8.9], [-8.899, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9.149, 9.15], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 210, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Static", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800.93, 644.227, 0], "ix": 2}, "a": {"a": 0, "k": [624.68, 396.878, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[114.65, 23.7], [-114.65, -23.7], [114.65, -23.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [990.4, 411.351], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [2.689, -0.824], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.537, -2.575], [0, 0], [0, 0], [0, 0]], "v": [[-2.313, 3.915], [8.446, 12.877], [11.135, -0.515], [12.642, -8.139], [7.908, -12.053], [0.269, -9.478], [-13.179, -4.944]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [193.968, 738.712], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 1.381], [1.381, 0]], "o": [[1.381, 0], [0, -1.38], [0, 0]], "v": [[15.074, -7.865], [17.574, -10.365], [15.074, -12.865]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.286, -0.504], [-0.504, 1.286]], "o": [[-0.503, 1.285], [1.286, 0.502], [0, 0]], "v": [[-17.071, 9.124], [-15.654, 12.364], [-12.413, 10.947]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [5.01, -12.796], [0, 0], [-12.522, 0]], "o": [[-14.669, 0], [0, 0], [4.292, -10.964], [0, 0]], "v": [[15.074, -12.865], [-17.071, 9.124], [-12.413, 10.947], [15.074, -7.865]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [251.238, 778.431], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 5, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.312, -0.427], [-0.428, 1.312]], "o": [[-0.428, 1.312], [1.313, 0.428], [0, 0]], "v": [[-50.478, 33.988], [-48.876, 37.139], [-45.724, 35.539]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 1.38], [1.38, 0]], "o": [[1.38, 0], [0, -1.381], [0, 0]], "v": [[48.406, -32.568], [50.906, -35.068], [48.406, -37.568]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [-44.045, 0], [0, 0], [13.545, -41.554]], "o": [[12.892, -39.549], [0, 0], [-46.264, 0], [0, 0]], "v": [[-45.724, 35.539], [48.406, -32.568], [48.406, -37.568], [-50.478, 33.988]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [314.414, 733.302], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 5, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[54.531, -16.085], [-54.531, -16.085], [-54.531, 16.085], [54.531, 16.085]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [236.89, 773.166], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.22, 5.664], [-13.422, 0], [-4.651, -11.97], [5.919, 0]], "o": [[-6.024, 0], [4.65, -11.97], [13.423, 0], [2.219, 5.664], [0, 0]], "v": [[-21.772, 16.085], [-29.593, 4.328], [0, -16.084], [29.594, 4.328], [21.772, 16.085]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [181.97, 773.165], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.434, -0.72], [0, 0], [0.326, 0.618], [0, 0], [-0.869, 0], [0, 0]], "o": [[0, 0], [-0.434, 0.721], [0, 0], [-0.434, -0.72], [0, 0], [0.869, 0]], "v": [[4.939, -2.779], [0.814, 3.704], [-1.031, 3.704], [-4.939, -2.881], [-3.962, -4.424], [4.069, -4.321]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [121.54, 784.544], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.184, 0.711], [0.711, -1.184]], "o": [[0.71, -1.184], [-1.184, -0.71], [0, 0]], "v": [[8.03, -3.427], [7.172, -6.857], [3.742, -6]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.89, -0.011], [-0.457, 0.763]], "o": [[0, 0], [0.439, 0.773], [0.889, 0.012], [0, 0]], "v": [[-0.108, 5.272], [-2.284, 6.505], [-0.139, 7.772], [2.035, 6.559]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [1.202, -0.68], [-0.681, -1.201]], "o": [[-0.68, -1.202], [-1.201, 0.68], [0, 0]], "v": [[-3.708, -6.159], [-7.116, -7.103], [-8.059, -3.696]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.742, -6], [-2.251, 3.986], [2.035, 6.559], [8.03, -3.427]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.068, 4.041], [-3.708, -6.159], [-8.059, -3.696], [-2.284, 6.505]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [145.086, 763.863], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 7, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.155, 0.758], [0.758, -1.154]], "o": [[0.757, -1.154], [-1.155, -0.757], [0, 0]], "v": [[7.978, -2.969], [7.259, -6.43], [3.797, -5.71]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.858, -0.015], [-0.471, 0.717]], "o": [[0, 0], [0.445, 0.733], [0.858, 0.014], [0, 0]], "v": [[-0.159, 4.878], [-2.295, 6.177], [-0.202, 7.378], [1.932, 6.25]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [1.18, -0.717], [-0.717, -1.18]], "o": [[-0.717, -1.18], [-1.18, 0.718], [0, 0]], "v": [[-3.745, -5.836], [-7.18, -6.674], [-8.018, -3.24]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.797, -5.71], [-2.249, 3.507], [1.932, 6.25], [7.978, -2.969]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.978, 3.58], [-3.745, -5.836], [-8.018, -3.24], [-2.295, 6.177]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [102.717, 762.688], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 7, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [4.88, -1.701], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0.955, -4.997], [0, 0], [0, 0], [0, 0]], "v": [[-4.296, 7.654], [15.648, 24.983], [20.634, -1.063], [23.392, -15.734], [14.693, -23.282], [0.583, -18.392], [-24.346, -9.674]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [195.722, 735.236], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.276, 6.806], [-33.18, 0], [-6.062, -31.473], [6.913, 0]], "o": [[-6.913, 0], [6.062, -31.366], [33.18, 0], [1.276, 6.806], [0, 0]], "v": [[-56.044, 34.131], [-66.892, 21.053], [0, -34.13], [66.892, 21.053], [56.044, 34.131]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [151.366, 755.119], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 2, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-73.254, 0], [0, -73.253]], "o": [[0, -73.253], [73.254, 0], [0, 0]], "v": [[-132.6, 66.3], [0, -66.3], [132.6, 66.3]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [280.436, 722.95], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 2, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-4.88, -1.701], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-0.955, -4.997], [0, 0], [0, 0], [0, 0]], "v": [[4.297, 7.654], [-15.647, 24.983], [-20.634, -1.063], [-23.392, -15.734], [-14.693, -23.282], [-0.583, -18.392], [24.346, -9.674]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [109.366, 735.236], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 2, "cix": 2, "bm": 0, "ix": 13, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.22, 5.664], [-13.422, 0], [-4.651, -11.97], [5.919, 0]], "o": [[-6.024, 0], [4.65, -11.97], [13.423, 0], [2.219, 5.664], [0, 0]], "v": [[-21.772, 16.085], [-29.593, 4.328], [0, -16.084], [29.594, 4.328], [21.772, 16.085]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [85.462, 773.165], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 14", "np": 2, "cix": 2, "bm": 0, "ix": 14, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.757, 1.156], [1.155, 0.757]], "o": [[1.155, 0.756], [0.756, -1.154], [0, 0]], "v": [[22.531, 17.741], [25.992, 17.018], [25.269, 13.557]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0.756, -1.154], [-1.155, -0.757]], "o": [[-1.154, -0.756], [-0.756, 1.156], [0, 0]], "v": [[-22.531, -17.742], [-25.992, -17.021], [-25.27, -13.559]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[25.269, 13.557], [-22.531, -17.742], [-25.27, -13.559], [22.531, 17.741]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [762.15, 514.001], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 15", "np": 5, "cix": 2, "bm": 0, "ix": 15, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-71, 18.8], [71, 18.8], [-71, -18.801]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [715.051, 692.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 16", "np": 2, "cix": 2, "bm": 0, "ix": 16, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.005, -0.006]], "o": [[0, 0], [-0.005, 0.007], [0, 0]], "v": [[213.97, -164.825], [211.922, -166.259], [211.908, -166.24]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[192.97, -134.225], [195.029, -132.807], [195.032, -132.81]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[166.37, -142.425], [168.428, -141.005], [168.43, -141.009]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[192.07, -179.825], [190.011, -181.242], [190.009, -181.241]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.007, -0.005]], "o": [[0, 0], [0.008, 0.006], [0, 0]], "v": [[210.57, -183.225], [209.136, -181.177], [209.159, -181.161]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[211.908, -166.24], [190.908, -135.639], [195.032, -132.81], [216.032, -163.41]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -66.706], [0, 0], [-37.313, 54.183]], "o": [[-37.887, 55.018], [0, 0], [0, -65.693], [0, 0]], "v": [[190.911, -135.643], [132.77, 51.275], [137.77, 51.275], [195.029, -132.807]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[132.77, 159.275], [137.77, 159.275], [137.77, 51.275], [132.77, 51.275]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0]], "o": [[0, 0]], "v": [[132.77, 51.275]], "c": false}, "ix": 2}, "nm": "Path 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 9, "ty": "sh", "ix": 10, "ks": {"a": 0, "k": {"i": [[0, 0], [14.325, 0], [0, 0], [0, 17.187]], "o": [[0, 14.414], [0, 0], [17.075, 0], [0, 0]], "v": [[132.77, 159.275], [106.77, 185.275], [106.77, 190.275], [137.77, 159.275]], "c": true}, "ix": 2}, "nm": "Path 10", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 10, "ty": "sh", "ix": 11, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[106.77, 185.275], [-205.23, 185.275], [-205.23, 190.275], [106.77, 190.275]], "c": true}, "ix": 2}, "nm": "Path 11", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 11, "ty": "sh", "ix": 12, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 5.919], [0, 0], [-8.681, 0]], "o": [[-5.919, 0], [0, 0], [0, 8.681], [0, 0]], "v": [[-205.23, 185.275], [-216.03, 174.475], [-221.03, 174.475], [-205.23, 190.275]], "c": true}, "ix": 2}, "nm": "Path 12", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 12, "ty": "sh", "ix": 13, "ks": {"a": 0, "k": {"i": [[0, 0], [-5.919, 0], [0, 0], [0, -8.68]], "o": [[0, -5.92], [0, 0], [-8.681, 0], [0, 0]], "v": [[-216.03, 174.475], [-205.23, 163.675], [-205.23, 158.675], [-221.03, 174.475]], "c": true}, "ix": 2}, "nm": "Path 13", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 13, "ty": "sh", "ix": 14, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-205.23, 163.675], [92.17, 163.675], [92.17, 158.675], [-205.23, 158.675]], "c": true}, "ix": 2}, "nm": "Path 14", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 14, "ty": "sh", "ix": 15, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 10.481], [0, 0], [7.719, 0]], "o": [[10.481, 0], [0, 0], [0, 7.719], [0, 0]], "v": [[92.17, 163.675], [111.17, 144.675], [106.17, 144.675], [92.17, 158.675]], "c": true}, "ix": 2}, "nm": "Path 15", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 15, "ty": "sh", "ix": 16, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[111.17, 43.075], [106.17, 43.075], [106.17, 144.676], [111.17, 144.676]], "c": true}, "ix": 2}, "nm": "Path 16", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 16, "ty": "sh", "ix": 17, "ks": {"a": 0, "k": {"i": [[0, 0]], "o": [[0, 0]], "v": [[111.17, 144.675]], "c": false}, "ix": 2}, "nm": "Path 17", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 17, "ty": "sh", "ix": 18, "ks": {"a": 0, "k": {"i": [[0, 0], [-37.312, 54.081], [0, 0], [0, -66.805]], "o": [[0, -65.795], [0, 0], [-37.889, 54.919], [0, 0]], "v": [[111.17, 43.075], [168.428, -141.005], [164.312, -143.845], [106.17, 43.075]], "c": true}, "ix": 2}, "nm": "Path 18", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 18, "ty": "sh", "ix": 19, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[168.43, -141.009], [194.13, -178.409], [190.009, -181.241], [164.31, -143.841]], "c": true}, "ix": 2}, "nm": "Path 19", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 19, "ty": "sh", "ix": 20, "ks": {"a": 0, "k": {"i": [[0, 0], [-4.854, -3.398], [0, 0], [4.98, -7.235]], "o": [[3.419, -4.965], [0, 0], [-7.145, -5.002], [0, 0]], "v": [[194.129, -178.407], [209.136, -181.177], [212.003, -185.273], [190.011, -181.242]], "c": true}, "ix": 2}, "nm": "Path 20", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 20, "ty": "sh", "ix": 21, "ks": {"a": 0, "k": {"i": [[0, 0], [3.389, -4.841], [0, 0], [7.13, 4.872]], "o": [[4.871, 3.328], [0, 0], [5.012, -7.159], [0, 0]], "v": [[209.159, -181.161], [211.922, -166.259], [216.018, -163.391], [211.981, -185.289]], "c": true}, "ix": 2}, "nm": "Path 21", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [1028.081, 368.375], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 17", "np": 23, "cix": 2, "bm": 0, "ix": 17, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.2, -6], [0, 0], [0, -66.2], [0, 0], [15.7, 0], [0, 0], [0, 7.3], [-7.3, 0], [0, 0], [0, 9.1], [0, 0], [-37.6, 54.5], [0, 0], [-6, -4.2]], "o": [[0, 0], [-37.6, 54.6], [0, 0], [0, 15.8], [0, 0], [-7.3, 0], [0, -7.3], [0, 0], [9.1, 0], [0, 0], [0, -66.3], [0, 0], [4.2, -6.1], [6, 4.1]], "v": [[214.15, -165], [193.15, -134.4], [135.45, 51.1], [135.45, 159.1], [106.95, 187.6], [-205.05, 187.6], [-218.35, 174.3], [-205.05, 161], [92.35, 161], [108.85, 144.5], [108.85, 42.9], [166.55, -142.6], [192.25, -180], [210.75, -183.4]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [1027.901, 368.55], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 18", "np": 2, "cix": 2, "bm": 0, "ix": 18, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.381, 0], [0, -1.381]], "o": [[0, -1.381], [-1.381, 0], [0, 0]], "v": [[2.5, -218.05], [0, -220.55], [-2.5, -218.05]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.381, 0], [0, 1.38]], "o": [[0, 1.38], [1.381, 0], [0, 0]], "v": [[-2.5, 218.05], [0, 220.55], [2.5, 218.05]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.5, 218.05], [2.5, 218.05], [2.5, -218.05], [-2.5, -218.05]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [715.051, 572.601], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 19", "np": 5, "cix": 2, "bm": 0, "ix": 19, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.381, 0], [0, -1.381]], "o": [[0, -1.381], [-1.381, 0], [0, 0]], "v": [[2.5, -218.05], [0, -220.55], [-2.5, -218.05]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.381, 0], [0, 1.38]], "o": [[0, 1.38], [1.381, 0], [0, 0]], "v": [[-2.5, 218.05], [0, 220.55], [2.5, 218.05]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.5, 218.05], [2.5, 218.05], [2.5, -218.05], [-2.5, -218.05]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [277.852, 572.601], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 20", "np": 5, "cix": 2, "bm": 0, "ix": 20, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.601, 0], [0, 0], [0, 0], [0, 0], [-1.6, 0.101], [-0.899, 36.3], [0, 0], [0, 0]], "o": [[1.601, -0.1], [0, 0], [0, 0], [0, 0], [1.601, 0], [36.9, -2.5], [0, 0], [0, 0], [0, 0]], "v": [[119.45, -70.85], [124.251, -71.049], [-195.15, -71.049], [-195.15, 71.049], [124.15, 71.049], [128.95, 70.85], [195.15, 1.85], [195.15, 0.05], [195.15, -70.85]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[53.15, 1.85], [53.15, -0.15]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.889999988032, 0.823999980852, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Pink').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [910.2, 458.6], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 21", "np": 4, "cix": 2, "bm": 0, "ix": 21, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.1, -2.2], [0, 0], [0, 0], [0, 0], [-38.1, 2.6], [-2.6, -39.1]], "o": [[0, 0], [0, 0], [0, 0], [-1.101, -37.899], [39.1, -2.599], [0.2, 2.301]], "v": [[71.5, -87.55], [71.5, 162.95], [-70.5, 162.95], [-70.5, -87.55], [-4.3, -160.35], [71.3, -94.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.889999988032, 0.823999980852, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Pink').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [714.549, 548], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 22", "np": 2, "cix": 2, "bm": 0, "ix": 22, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.5, -31.7], [3.8, -8.2], [0, 0], [0, 0], [0, 0], [0, 0], [-23, 1.1]], "o": [[0.5, 9.6], [0, 0], [0, 0], [0, 0], [0, 0], [8.199, -20], [31.7, -1.6]], "v": [[101.15, -98.2], [95.85, -71.3], [4.751, 154.4], [-101.65, 111.5], [-9.35, -117.2], [-9.249, -117.2], [41.15, -152.8]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [949.9, 155.65], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 23", "np": 2, "cix": 2, "bm": 0, "ix": 23, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.196, -0.69], [-0.69, 1.196]], "o": [[-0.69, 1.195], [1.195, 0.69], [0, 0]], "v": [[-137.565, 115.772], [-136.65, 119.187], [-133.235, 118.272]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.893, 0], [0.447, -0.774]], "o": [[0, 0], [-0.446, -0.774], [-0.893, 0], [0, 0]], "v": [[0, -117.378], [2.164, -118.628], [0, -119.878], [-2.165, -118.628]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[67.7, -0.178], [65.536, 1.073]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.196, 0.69], [0.69, 1.195]], "o": [[0.691, 1.196], [1.195, -0.69], [0, 0]], "v": [[133.235, 118.272], [136.651, 119.187], [137.565, 115.772]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-133.235, 118.272], [-65.535, 1.073], [-69.865, -1.428], [-137.565, 115.772]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-65.535, 1.073], [2.164, -116.127], [-2.165, -118.628], [-69.865, -1.428]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.165, -116.127], [65.536, 1.073], [69.865, -1.428], [2.164, -118.628]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[65.536, 1.073], [133.235, 118.272], [137.565, 115.772], [69.865, -1.428]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [983.65, 673.628], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 24", "np": 10, "cix": 2, "bm": 0, "ix": 24, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-1.228, 0.632]], "o": [[0, 0], [0.633, 1.228], [0, 0]], "v": [[51.454, 111.798], [49.23, 112.943], [52.598, 114.021]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.632, -1.227]], "o": [[0, 0], [-1.228, 0.633], [0, 0]], "v": [[-61.145, -106.807], [-62.288, -109.03], [-63.366, -105.662]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [1.227, -0.633]], "o": [[0, 0], [-0.632, -1.227], [0, 0]], "v": [[-51.454, -111.798], [-49.231, -112.943], [-52.598, -114.02]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.632, 1.228]], "o": [[0, 0], [1.228, -0.632], [0, 0]], "v": [[61.143, 106.807], [62.288, 109.029], [63.366, 105.662]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[53.676, 110.653], [-58.921, -107.952], [-63.366, -105.662], [49.23, 112.943]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-59.999, -104.584], [-50.309, -109.576], [-52.598, -114.02], [-62.288, -109.03]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-53.676, -110.653], [58.921, 107.952], [63.366, 105.662], [-49.231, -112.943]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[59.999, 104.585], [50.309, 109.576], [52.598, 114.021], [62.288, 109.029]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [317.378, 209.85], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 25", "np": 10, "cix": 2, "bm": 0, "ix": 25, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -1.38]], "o": [[0, 0], [-1.381, 0], [0, 0]], "v": [[-122.95, -5.45], [-122.95, -7.95], [-125.45, -5.45]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [1.38, 0]], "o": [[0, 0], [0, -1.38], [0, 0]], "v": [[122.95, -5.45], [125.45, -5.45], [122.95, -7.95]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 1.381]], "o": [[0, 0], [1.38, 0], [0, 0]], "v": [[122.95, 5.45], [122.95, 7.95], [125.45, 5.45]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-1.381, 0]], "o": [[0, 0], [0, 1.381], [0, 0]], "v": [[-122.95, 5.45], [-125.45, 5.45], [-122.95, 7.95]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-122.95, -2.95], [122.95, -2.95], [122.95, -7.95], [-122.95, -7.95]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[120.45, 5.45], [125.45, 5.45], [125.45, -5.45], [120.45, -5.45]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0]], "o": [[0, 0]], "v": [[120.45, -5.45]], "c": false}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[122.95, 2.95], [-122.95, 2.95], [-122.95, 7.95], [122.95, 7.95]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-120.45, -5.45], [-125.45, -5.45], [-125.45, 5.45], [-120.45, 5.45]], "c": true}, "ix": 2}, "nm": "Path 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [501.501, 322.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 26", "np": 11, "cix": 2, "bm": 0, "ix": 26, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-143.146, -37.196], [45.847, 140.615], [143.146, 37.195], [-45.848, -140.615]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [809.57, 549.474], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 27", "np": 2, "cix": 2, "bm": 0, "ix": 27, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -1.38]], "o": [[0, 0], [-1.381, 0], [0, 0]], "v": [[-246.75, -13.301], [-246.75, -15.801], [-249.25, -13.301]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [1.381, 0]], "o": [[0, 0], [0, -1.38], [0, 0]], "v": [[246.75, -13.301], [249.25, -13.301], [246.75, -15.801]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 1.38]], "o": [[0, 0], [1.381, 0], [0, 0]], "v": [[246.75, 13.301], [246.75, 15.801], [249.25, 13.301]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-1.381, 0]], "o": [[0, 0], [0, 1.38], [0, 0]], "v": [[-246.75, 13.301], [-249.25, 13.301], [-246.75, 15.801]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-246.75, -10.801], [246.75, -10.801], [246.75, -15.801], [-246.75, -15.801]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[244.25, 13.301], [249.25, 13.301], [249.25, -13.301], [244.25, -13.301]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0]], "o": [[0, 0]], "v": [[244.25, -13.301]], "c": false}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[246.75, 10.8], [-246.75, 10.8], [-246.75, 15.8], [246.75, 15.8]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-244.25, -13.301], [-249.25, -13.301], [-249.25, 13.301], [-244.25, 13.301]], "c": true}, "ix": 2}, "nm": "Path 9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [496.398, 341.251], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 28", "np": 11, "cix": 2, "bm": 0, "ix": 28, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.381, 0], [0, -1.381]], "o": [[0, -1.381], [-1.381, 0], [0, 0]], "v": [[2.5, -1], [0, -3.5], [-2.5, -1]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.381, 0], [0, 1.381]], "o": [[0, 1.381], [1.381, 0], [0, 0]], "v": [[-2.5, 1], [0, 3.5], [2.5, 1]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.5, -1], [-2.5, -0.996], [-2.5, -0.992], [-2.5, -0.988], [-2.5, -0.984], [-2.5, -0.979], [-2.5, -0.976], [-2.5, -0.972], [-2.5, -0.968], [-2.5, -0.964], [-2.5, -0.959], [-2.5, -0.955], [-2.5, -0.951], [-2.5, -0.947], [-2.5, -0.943], [-2.5, -0.939], [-2.5, -0.935], [-2.5, -0.931], [-2.5, -0.927], [-2.5, -0.923], [-2.5, -0.919], [-2.5, -0.915], [-2.5, -0.91], [-2.5, -0.906], [-2.5, -0.902], [-2.5, -0.898], [-2.5, -0.895], [-2.5, -0.891], [-2.5, -0.887], [-2.5, -0.883], [-2.5, -0.878], [-2.5, -0.874], [-2.5, -0.87], [-2.5, -0.866], [-2.5, -0.862], [-2.5, -0.858], [-2.5, -0.854], [-2.5, -0.85], [-2.5, -0.846], [-2.5, -0.842], [-2.5, -0.838], [-2.5, -0.834], [-2.5, -0.83], [-2.5, -0.826], [-2.5, -0.822], [-2.5, -0.818], [-2.5, -0.814], [-2.5, -0.81], [-2.5, -0.806], [-2.5, -0.802], [-2.5, -0.798], [-2.5, -0.794], [-2.5, -0.79], [-2.5, -0.786], [-2.5, -0.782], [-2.5, -0.778], [-2.5, -0.774], [-2.5, -0.77], [-2.5, -0.766], [-2.5, -0.762], [-2.5, -0.758], [-2.5, -0.754], [-2.5, -0.75], [-2.5, -0.746], [-2.5, -0.742], [-2.5, -0.738], [-2.5, -0.734], [-2.5, -0.73], [-2.5, -0.727], [-2.5, -0.723], [-2.5, -0.719], [-2.5, -0.715], [-2.5, -0.71], [-2.5, -0.706], [-2.5, -0.702], [-2.5, -0.698], [-2.5, -0.694], [-2.5, -0.69], [-2.5, -0.687], [-2.5, -0.683], [-2.5, -0.679], [-2.5, -0.675], [-2.5, -0.671], [-2.5, -0.667], [-2.5, -0.663], [-2.5, -0.659], [-2.5, -0.655], [-2.5, -0.651], [-2.5, -0.647], [-2.5, -0.644], [-2.5, -0.64], [-2.5, -0.636], [-2.5, -0.632], [-2.5, -0.628], [-2.5, -0.624], [-2.5, -0.62], [-2.5, -0.616], [-2.5, -0.612], [-2.5, -0.608], [-2.5, -0.604], [-2.5, -0.601], [-2.5, -0.597], [-2.5, -0.593], [-2.5, -0.589], [-2.5, -0.585], [-2.5, -0.58], [-2.5, -0.576], [-2.5, -0.572], [-2.5, -0.568], [-2.5, -0.564], [-2.5, -0.561], [-2.5, -0.557], [-2.5, -0.554], [-2.5, -0.55], [-2.5, -0.546], [-2.5, -0.542], [-2.5, -0.538], [-2.5, -0.534], [-2.5, -0.53], [-2.5, -0.526], [-2.5, -0.522], [-2.5, -0.519], [-2.5, -0.515], [-2.5, -0.511], [-2.5, -0.507], [-2.5, -0.503], [-2.5, -0.499], [-2.5, -0.495], [-2.5, -0.491], [-2.5, -0.487], [-2.5, -0.483], [-2.5, -0.479], [-2.5, -0.476], [-2.5, -0.472], [-2.5, -0.468], [-2.5, -0.464], [-2.5, -0.46], [-2.5, -0.456], [-2.5, -0.452], [-2.5, -0.448], [-2.5, -0.444], [-2.5, -0.44], [-2.5, -0.437], [-2.5, -0.433], [-2.5, -0.429], [-2.5, -0.425], [-2.5, -0.421], [-2.5, -0.417], [-2.5, -0.413], [-2.5, -0.41], [-2.5, -0.406], [-2.5, -0.402], [-2.5, -0.398], [-2.5, -0.395], [-2.5, -0.391], [-2.5, -0.387], [-2.5, -0.383], [-2.5, -0.379], [-2.5, -0.375], [-2.5, -0.371], [-2.5, -0.367], [-2.5, -0.363], [-2.5, -0.359], [-2.5, -0.355], [-2.5, -0.352], [-2.5, -0.348], [-2.5, -0.344], [-2.5, -0.341], [-2.5, -0.337], [-2.5, -0.333], [-2.5, -0.329], [-2.5, -0.325], [-2.5, -0.321], [-2.5, -0.317], [-2.5, -0.313], [-2.5, -0.31], [-2.5, -0.306], [-2.5, -0.302], [-2.5, -0.298], [-2.5, -0.294], [-2.5, -0.29], [-2.5, -0.287], [-2.5, -0.283], [-2.5, -0.279], [-2.5, -0.275], [-2.5, -0.271], [-2.5, -0.268], [-2.5, -0.264], [-2.5, -0.26], [-2.5, -0.256], [-2.5, -0.252], [-2.5, -0.248], [-2.5, -0.244], [-2.5, -0.24], [-2.5, -0.237], [-2.5, -0.233], [-2.5, -0.229], [-2.5, -0.226], [-2.5, -0.222], [-2.5, -0.218], [-2.5, -0.214], [-2.5, -0.21], [-2.5, -0.206], [-2.5, -0.202], [-2.5, -0.198], [-2.5, -0.194], [-2.5, -0.191], [-2.5, -0.188], [-2.5, -0.184], [-2.5, -0.18], [-2.5, -0.176], [-2.5, -0.172], [-2.5, -0.168], [-2.5, -0.164], [-2.5, -0.16], [-2.5, -0.156], [-2.5, -0.152], [-2.5, -0.149], [-2.5, -0.146], [-2.5, -0.142], [-2.5, -0.138], [-2.5, -0.134], [-2.5, -0.13], [-2.5, -0.126], [-2.5, -0.122], [-2.5, -0.118], [-2.5, -0.114], [-2.5, -0.111], [-2.5, -0.107], [-2.5, -0.104], [-2.5, -0.1], [-2.5, -0.096], [-2.5, -0.092], [-2.5, -0.088], [-2.5, -0.084], [-2.5, -0.08], [-2.5, -0.076], [-2.5, -0.072], [-2.5, -0.069], [-2.5, -0.065], [-2.5, -0.062], [-2.5, -0.058], [-2.5, -0.054], [-2.5, -0.05], [-2.5, -0.046], [-2.5, -0.042], [-2.5, -0.038], [-2.5, -0.034], [-2.5, -0.031], [-2.5, -0.027], [-2.5, -0.023], [-2.5, -0.02], [-2.5, -0.016], [-2.5, -0.012], [-2.5, -0.008], [-2.5, -0.004], [-2.5, 0], [-2.5, 0.004], [-2.5, 0.007], [-2.5, 0.011], [-2.5, 0.015], [-2.5, 0.019], [-2.5, 0.022], [-2.5, 0.026], [-2.5, 0.03], [-2.5, 0.034], [-2.5, 0.038], [-2.5, 0.042], [-2.5, 0.045], [-2.5, 0.049], [-2.5, 0.053], [-2.5, 0.057], [-2.5, 0.061], [-2.5, 0.064], [-2.5, 0.068], [-2.5, 0.072], [-2.5, 0.076], [-2.5, 0.08], [-2.5, 0.083], [-2.5, 0.087], [-2.5, 0.091], [-2.5, 0.095], [-2.5, 0.099], [-2.5, 0.103], [-2.5, 0.106], [-2.5, 0.11], [-2.5, 0.114], [-2.5, 0.118], [-2.5, 0.121], [-2.5, 0.125], [-2.5, 0.129], [-2.5, 0.133], [-2.5, 0.137], [-2.5, 0.141], [-2.5, 0.145], [-2.5, 0.148], [-2.5, 0.152], [-2.5, 0.156], [-2.5, 0.16], [-2.5, 0.163], [-2.5, 0.167], [-2.5, 0.171], [-2.5, 0.175], [-2.5, 0.179], [-2.5, 0.183], [-2.5, 0.187], [-2.5, 0.19], [-2.5, 0.194], [-2.5, 0.198], [-2.5, 0.202], [-2.5, 0.205], [-2.5, 0.209], [-2.5, 0.213], [-2.5, 0.217], [-2.5, 0.221], [-2.5, 0.225], [-2.5, 0.229], [-2.5, 0.232], [-2.5, 0.236], [-2.5, 0.24], [-2.5, 0.244], [-2.5, 0.248], [-2.5, 0.251], [-2.5, 0.255], [-2.5, 0.259], [-2.5, 0.263], [-2.5, 0.267], [-2.5, 0.271], [-2.5, 0.274], [-2.5, 0.278], [-2.5, 0.282], [-2.5, 0.286], [-2.5, 0.29], [-2.5, 0.294], [-2.5, 0.298], [-2.5, 0.301], [-2.5, 0.305], [-2.5, 0.309], [-2.5, 0.312], [-2.5, 0.316], [-2.5, 0.32], [-2.5, 0.324], [-2.5, 0.328], [-2.5, 0.332], [-2.5, 0.336], [-2.5, 0.34], [-2.5, 0.344], [-2.5, 0.348], [-2.5, 0.352], [-2.5, 0.355], [-2.5, 0.359], [-2.5, 0.362], [-2.5, 0.366], [-2.5, 0.37], [-2.5, 0.374], [-2.5, 0.378], [-2.5, 0.382], [-2.5, 0.386], [-2.5, 0.39], [-2.5, 0.394], [-2.5, 0.397], [-2.5, 0.401], [-2.5, 0.405], [-2.5, 0.409], [-2.5, 0.413], [-2.5, 0.417], [-2.5, 0.421], [-2.5, 0.425], [-2.5, 0.429], [-2.5, 0.433], [-2.5, 0.436], [-2.5, 0.439], [-2.5, 0.443], [-2.5, 0.447], [-2.5, 0.451], [-2.5, 0.455], [-2.5, 0.459], [-2.5, 0.463], [-2.5, 0.467], [-2.5, 0.471], [-2.5, 0.475], [-2.5, 0.479], [-2.5, 0.482], [-2.5, 0.486], [-2.5, 0.49], [-2.5, 0.494], [-2.5, 0.498], [-2.5, 0.502], [-2.5, 0.506], [-2.5, 0.51], [-2.5, 0.514], [-2.5, 0.518], [-2.5, 0.521], [-2.5, 0.525], [-2.5, 0.529], [-2.5, 0.533], [-2.5, 0.537], [-2.5, 0.541], [-2.5, 0.545], [-2.5, 0.549], [-2.5, 0.553], [-2.5, 0.557], [-2.5, 0.561], [-2.5, 0.564], [-2.5, 0.568], [-2.5, 0.572], [-2.5, 0.576], [-2.5, 0.58], [-2.5, 0.584], [-2.5, 0.588], [-2.5, 0.592], [-2.5, 0.596], [-2.5, 0.6], [-2.5, 0.604], [-2.5, 0.607], [-2.5, 0.611], [-2.5, 0.615], [-2.5, 0.619], [-2.5, 0.623], [-2.5, 0.627], [-2.5, 0.631], [-2.5, 0.635], [-2.5, 0.639], [-2.5, 0.643], [-2.5, 0.646], [-2.5, 0.65], [-2.5, 0.654], [-2.5, 0.658], [-2.5, 0.662], [-2.5, 0.666], [-2.5, 0.67], [-2.5, 0.674], [-2.5, 0.678], [-2.5, 0.682], [-2.5, 0.686], [-2.5, 0.689], [-2.5, 0.694], [-2.5, 0.698], [-2.5, 0.702], [-2.5, 0.706], [-2.5, 0.71], [-2.5, 0.714], [-2.5, 0.718], [-2.5, 0.722], [-2.5, 0.726], [-2.5, 0.729], [-2.5, 0.733], [-2.5, 0.737], [-2.5, 0.741], [-2.5, 0.745], [-2.5, 0.749], [-2.5, 0.753], [-2.5, 0.758], [-2.5, 0.762], [-2.5, 0.766], [-2.5, 0.77], [-2.5, 0.773], [-2.5, 0.777], [-2.5, 0.781], [-2.5, 0.785], [-2.5, 0.789], [-2.5, 0.793], [-2.5, 0.797], [-2.5, 0.801], [-2.5, 0.806], [-2.5, 0.81], [-2.5, 0.813], [-2.5, 0.817], [-2.5, 0.821], [-2.5, 0.825], [-2.5, 0.829], [-2.5, 0.833], [-2.5, 0.837], [-2.5, 0.842], [-2.5, 0.846], [-2.5, 0.85], [-2.5, 0.854], [-2.5, 0.857], [-2.5, 0.861], [-2.5, 0.865], [-2.5, 0.869], [-2.5, 0.874], [-2.5, 0.878], [-2.5, 0.882], [-2.5, 0.886], [-2.5, 0.89], [-2.5, 0.894], [-2.5, 0.897], [-2.5, 0.902], [-2.5, 0.906], [-2.5, 0.91], [-2.5, 0.914], [-2.5, 0.918], [-2.5, 0.922], [-2.5, 0.926], [-2.5, 0.931], [-2.5, 0.935], [-2.5, 0.938], [-2.5, 0.942], [-2.5, 0.946], [-2.5, 0.95], [-2.5, 0.955], [-2.5, 0.959], [-2.5, 0.963], [-2.5, 0.967], [-2.5, 0.971], [-2.5, 0.975], [-2.5, 0.979], [-2.5, 0.983], [-2.5, 0.987], [-2.5, 0.991], [-2.5, 0.995], [-2.5, 1], [2.5, 1], [2.5, 0.995], [2.5, 0.991], [2.5, 0.987], [2.5, 0.983], [2.5, 0.979], [2.5, 0.975], [2.5, 0.971], [2.5, 0.967], [2.5, 0.963], [2.5, 0.959], [2.5, 0.955], [2.5, 0.95], [2.5, 0.946], [2.5, 0.942], [2.5, 0.938], [2.5, 0.935], [2.5, 0.931], [2.5, 0.926], [2.5, 0.922], [2.5, 0.918], [2.5, 0.914], [2.5, 0.91], [2.5, 0.906], [2.5, 0.902], [2.5, 0.897], [2.5, 0.894], [2.5, 0.89], [2.5, 0.886], [2.5, 0.882], [2.5, 0.878], [2.5, 0.874], [2.5, 0.869], [2.5, 0.865], [2.5, 0.861], [2.5, 0.857], [2.5, 0.854], [2.5, 0.85], [2.5, 0.846], [2.5, 0.842], [2.5, 0.837], [2.5, 0.833], [2.5, 0.829], [2.5, 0.825], [2.5, 0.821], [2.5, 0.817], [2.5, 0.813], [2.5, 0.81], [2.5, 0.806], [2.5, 0.801], [2.5, 0.797], [2.5, 0.793], [2.5, 0.789], [2.5, 0.785], [2.5, 0.781], [2.5, 0.777], [2.5, 0.773], [2.5, 0.77], [2.5, 0.766], [2.5, 0.762], [2.5, 0.758], [2.5, 0.753], [2.5, 0.749], [2.5, 0.745], [2.5, 0.741], [2.5, 0.737], [2.5, 0.733], [2.5, 0.729], [2.5, 0.726], [2.5, 0.722], [2.5, 0.718], [2.5, 0.714], [2.5, 0.71], [2.5, 0.706], [2.5, 0.702], [2.5, 0.698], [2.5, 0.694], [2.5, 0.689], [2.5, 0.686], [2.5, 0.682], [2.5, 0.678], [2.5, 0.674], [2.5, 0.67], [2.5, 0.666], [2.5, 0.662], [2.5, 0.658], [2.5, 0.654], [2.5, 0.65], [2.5, 0.646], [2.5, 0.643], [2.5, 0.639], [2.5, 0.635], [2.5, 0.631], [2.5, 0.627], [2.5, 0.623], [2.5, 0.619], [2.5, 0.615], [2.5, 0.611], [2.5, 0.607], [2.5, 0.604], [2.5, 0.6], [2.5, 0.596], [2.5, 0.592], [2.5, 0.588], [2.5, 0.584], [2.5, 0.58], [2.5, 0.576], [2.5, 0.572], [2.5, 0.568], [2.5, 0.564], [2.5, 0.561], [2.5, 0.557], [2.5, 0.553], [2.5, 0.549], [2.5, 0.545], [2.5, 0.541], [2.5, 0.537], [2.5, 0.533], [2.5, 0.529], [2.5, 0.525], [2.5, 0.521], [2.5, 0.518], [2.5, 0.514], [2.5, 0.51], [2.5, 0.506], [2.5, 0.502], [2.5, 0.498], [2.5, 0.494], [2.5, 0.49], [2.5, 0.486], [2.5, 0.482], [2.5, 0.479], [2.5, 0.475], [2.5, 0.471], [2.5, 0.467], [2.5, 0.463], [2.5, 0.459], [2.5, 0.455], [2.5, 0.451], [2.5, 0.447], [2.5, 0.443], [2.5, 0.439], [2.5, 0.436], [2.5, 0.433], [2.5, 0.429], [2.5, 0.425], [2.5, 0.421], [2.5, 0.417], [2.5, 0.413], [2.5, 0.409], [2.5, 0.405], [2.5, 0.401], [2.5, 0.397], [2.5, 0.394], [2.5, 0.39], [2.5, 0.386], [2.5, 0.382], [2.5, 0.378], [2.5, 0.374], [2.5, 0.37], [2.5, 0.366], [2.5, 0.362], [2.5, 0.359], [2.5, 0.355], [2.5, 0.352], [2.5, 0.348], [2.5, 0.344], [2.5, 0.34], [2.5, 0.336], [2.5, 0.332], [2.5, 0.328], [2.5, 0.324], [2.5, 0.32], [2.5, 0.316], [2.5, 0.312], [2.5, 0.309], [2.5, 0.305], [2.5, 0.301], [2.5, 0.298], [2.5, 0.294], [2.5, 0.29], [2.5, 0.286], [2.5, 0.282], [2.5, 0.278], [2.5, 0.274], [2.5, 0.271], [2.5, 0.267], [2.5, 0.263], [2.5, 0.259], [2.5, 0.255], [2.5, 0.251], [2.5, 0.248], [2.5, 0.244], [2.5, 0.24], [2.5, 0.236], [2.5, 0.232], [2.5, 0.229], [2.5, 0.225], [2.5, 0.221], [2.5, 0.217], [2.5, 0.213], [2.5, 0.209], [2.5, 0.205], [2.5, 0.202], [2.5, 0.198], [2.5, 0.194], [2.5, 0.19], [2.5, 0.187], [2.5, 0.183], [2.5, 0.179], [2.5, 0.175], [2.5, 0.171], [2.5, 0.167], [2.5, 0.163], [2.5, 0.16], [2.5, 0.156], [2.5, 0.152], [2.5, 0.148], [2.5, 0.145], [2.5, 0.141], [2.5, 0.137], [2.5, 0.133], [2.5, 0.129], [2.5, 0.125], [2.5, 0.121], [2.5, 0.118], [2.5, 0.114], [2.5, 0.11], [2.5, 0.106], [2.5, 0.103], [2.5, 0.099], [2.5, 0.095], [2.5, 0.091], [2.5, 0.087], [2.5, 0.083], [2.5, 0.08], [2.5, 0.076], [2.5, 0.072], [2.5, 0.068], [2.5, 0.064], [2.5, 0.061], [2.5, 0.057], [2.5, 0.053], [2.5, 0.049], [2.5, 0.045], [2.5, 0.042], [2.5, 0.038], [2.5, 0.034], [2.5, 0.03], [2.5, 0.026], [2.5, 0.022], [2.5, 0.019], [2.5, 0.015], [2.5, 0.011], [2.5, 0.007], [2.5, 0.004], [2.5, 0], [2.5, -0.004], [2.5, -0.008], [2.5, -0.012], [2.5, -0.016], [2.5, -0.02], [2.5, -0.023], [2.5, -0.027], [2.5, -0.031], [2.5, -0.034], [2.5, -0.038], [2.5, -0.042], [2.5, -0.046], [2.5, -0.05], [2.5, -0.054], [2.5, -0.058], [2.5, -0.062], [2.5, -0.065], [2.5, -0.069], [2.5, -0.072], [2.5, -0.076], [2.5, -0.08], [2.5, -0.084], [2.5, -0.088], [2.5, -0.092], [2.5, -0.096], [2.5, -0.1], [2.5, -0.104], [2.5, -0.107], [2.5, -0.111], [2.5, -0.114], [2.5, -0.118], [2.5, -0.122], [2.5, -0.126], [2.5, -0.13], [2.5, -0.134], [2.5, -0.138], [2.5, -0.142], [2.5, -0.146], [2.5, -0.149], [2.5, -0.152], [2.5, -0.156], [2.5, -0.16], [2.5, -0.164], [2.5, -0.168], [2.5, -0.172], [2.5, -0.176], [2.5, -0.18], [2.5, -0.184], [2.5, -0.188], [2.5, -0.191], [2.5, -0.194], [2.5, -0.198], [2.5, -0.202], [2.5, -0.206], [2.5, -0.21], [2.5, -0.214], [2.5, -0.218], [2.5, -0.222], [2.5, -0.226], [2.5, -0.229], [2.5, -0.233], [2.5, -0.237], [2.5, -0.24], [2.5, -0.244], [2.5, -0.248], [2.5, -0.252], [2.5, -0.256], [2.5, -0.26], [2.5, -0.264], [2.5, -0.268], [2.5, -0.271], [2.5, -0.275], [2.5, -0.279], [2.5, -0.283], [2.5, -0.287], [2.5, -0.29], [2.5, -0.294], [2.5, -0.298], [2.5, -0.302], [2.5, -0.306], [2.5, -0.31], [2.5, -0.313], [2.5, -0.317], [2.5, -0.321], [2.5, -0.325], [2.5, -0.329], [2.5, -0.333], [2.5, -0.337], [2.5, -0.341], [2.5, -0.344], [2.5, -0.348], [2.5, -0.352], [2.5, -0.355], [2.5, -0.359], [2.5, -0.363], [2.5, -0.367], [2.5, -0.371], [2.5, -0.375], [2.5, -0.379], [2.5, -0.383], [2.5, -0.387], [2.5, -0.391], [2.5, -0.395], [2.5, -0.398], [2.5, -0.402], [2.5, -0.406], [2.5, -0.41], [2.5, -0.413], [2.5, -0.417], [2.5, -0.421], [2.5, -0.425], [2.5, -0.429], [2.5, -0.433], [2.5, -0.437], [2.5, -0.44], [2.5, -0.444], [2.5, -0.448], [2.5, -0.452], [2.5, -0.456], [2.5, -0.46], [2.5, -0.464], [2.5, -0.468], [2.5, -0.472], [2.5, -0.476], [2.5, -0.479], [2.5, -0.483], [2.5, -0.487], [2.5, -0.491], [2.5, -0.495], [2.5, -0.499], [2.5, -0.503], [2.5, -0.507], [2.5, -0.511], [2.5, -0.515], [2.5, -0.519], [2.5, -0.522], [2.5, -0.526], [2.5, -0.53], [2.5, -0.534], [2.5, -0.538], [2.5, -0.542], [2.5, -0.546], [2.5, -0.55], [2.5, -0.554], [2.5, -0.557], [2.5, -0.561], [2.5, -0.564], [2.5, -0.568], [2.5, -0.572], [2.5, -0.576], [2.5, -0.58], [2.5, -0.585], [2.5, -0.589], [2.5, -0.593], [2.5, -0.597], [2.5, -0.601], [2.5, -0.604], [2.5, -0.608], [2.5, -0.612], [2.5, -0.616], [2.5, -0.62], [2.5, -0.624], [2.5, -0.628], [2.5, -0.632], [2.5, -0.636], [2.5, -0.64], [2.5, -0.644], [2.5, -0.647], [2.5, -0.651], [2.5, -0.655], [2.5, -0.659], [2.5, -0.663], [2.5, -0.667], [2.5, -0.671], [2.5, -0.675], [2.5, -0.679], [2.5, -0.683], [2.5, -0.687], [2.5, -0.69], [2.5, -0.694], [2.5, -0.698], [2.5, -0.702], [2.5, -0.706], [2.5, -0.71], [2.5, -0.715], [2.5, -0.719], [2.5, -0.723], [2.5, -0.727], [2.5, -0.73], [2.5, -0.734], [2.5, -0.738], [2.5, -0.742], [2.5, -0.746], [2.5, -0.75], [2.5, -0.754], [2.5, -0.758], [2.5, -0.762], [2.5, -0.766], [2.5, -0.77], [2.5, -0.774], [2.5, -0.778], [2.5, -0.782], [2.5, -0.786], [2.5, -0.79], [2.5, -0.794], [2.5, -0.798], [2.5, -0.802], [2.5, -0.806], [2.5, -0.81], [2.5, -0.814], [2.5, -0.818], [2.5, -0.822], [2.5, -0.826], [2.5, -0.83], [2.5, -0.834], [2.5, -0.838], [2.5, -0.842], [2.5, -0.846], [2.5, -0.85], [2.5, -0.854], [2.5, -0.858], [2.5, -0.862], [2.5, -0.866], [2.5, -0.87], [2.5, -0.874], [2.5, -0.878], [2.5, -0.883], [2.5, -0.887], [2.5, -0.891], [2.5, -0.895], [2.5, -0.898], [2.5, -0.902], [2.5, -0.906], [2.5, -0.91], [2.5, -0.915], [2.5, -0.919], [2.5, -0.923], [2.5, -0.927], [2.5, -0.931], [2.5, -0.935], [2.5, -0.939], [2.5, -0.943], [2.5, -0.947], [2.5, -0.951], [2.5, -0.955], [2.5, -0.959], [2.5, -0.964], [2.5, -0.968], [2.5, -0.972], [2.5, -0.976], [2.5, -0.979], [2.5, -0.984], [2.5, -0.988], [2.5, -0.992], [2.5, -0.996], [2.5, -1]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.5, -1], [-2.5, -1], [-2.5, 1], [2.5, 1]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 2", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [963.352, 459.551], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 29", "np": 7, "cix": 2, "bm": 0, "ix": 29, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -1.381], [-1.381, 0]], "o": [[-1.381, 0], [0, 1.381], [0, 0]], "v": [[-608.75, -2.5], [-611.25, 0], [-608.75, 2.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 1.381], [1.381, 0]], "o": [[1.381, 0], [0, -1.381], [0, 0]], "v": [[608.75, 2.5], [611.25, 0], [608.75, -2.5]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-608.75, 2.5], [608.75, 2.5], [608.75, -2.5], [-608.75, -2.5]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [611.5, 790.65], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 30", "np": 5, "cix": 2, "bm": 0, "ix": 30, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-18, -16.9]], "o": [[0, 0], [0, 0], [0, 0], [-17, 18], [0, 0]], "v": [[-48.55, 101.15], [91.65, -47.65], [34.85, -101.15], [-74.65, 15.05], [-72.75, 78.35]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [917.596, 687.8], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 31", "np": 2, "cix": 2, "bm": 0, "ix": 31, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 1.381], [1.381, 0]], "o": [[1.381, 0], [0, -1.381], [0, 0]], "v": [[101.8, 2.5], [104.3, 0], [101.8, -2.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -1.381], [-1.38, 0]], "o": [[-1.38, 0], [0, 1.381], [0, 0]], "v": [[-101.8, -2.5], [-104.3, 0], [-101.8, 2.5]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[101.8, -2.5], [-101.8, -2.5], [-101.8, 2.5], [101.8, 2.5]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.030999998953, 0, 0.224000010771, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Dark Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [678.85, 767.85], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 32", "np": 5, "cix": 2, "bm": 0, "ix": 32, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, -25.5]], "o": [[0, 0], [0, 0], [0, 0], [-25.5, 0], [0, 0]], "v": [[-105.35, 40.2], [105.35, 40.2], [105.35, -40.199], [-59.15, -40.199], [-105.35, 6]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [680.702, 751.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 33", "np": 2, "cix": 2, "bm": 0, "ix": 33, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-44.5, 18.2], [-24, -58.5], [1.599, -17.7]], "o": [[0, 0], [0, 0], [0, 0], [-0.2, -45.3], [58.5, -24], [7.199, 17.6], [0, 0]], "v": [[114.901, 193.25], [-113.999, 194.45], [-115.299, -63.95], [-114.799, -63.95], [-43.701, -170.45], [105.601, -107.95], [113.701, -54.35]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0, 1, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Light Purple').effect('Fill')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [990.151, 194.7], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 34", "np": 2, "cix": 2, "bm": 0, "ix": 34, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 210, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 1, "nm": "Background", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "sw": 1600, "sh": 1200, "sc": "#ffffff", "ip": 210, "op": 210, "st": 0, "bm": 0, "hidden": 0}], "markers": []}