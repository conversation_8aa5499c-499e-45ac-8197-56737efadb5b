// README
// This is only a .env example
// Do not change this file, adding your GITHUB TOKEN here!
// Use cp or mv (like this: cp env.example .env), then edit .env with your GITHUB TOKEN.
// IMPORTANT: Don't forget to add to update your .gitignore with .env (to avoid making your key public!)

REACT_APP_GITHUB_TOKEN = "YOUR GITHUB TOKEN HERE"
GITHUB_USERNAME = "YOUR GITHUB USERNAME HERE"
// Set to true to fetch profile data from GitHub (remember to remove all components relying on GitHub data if set to false)
USE_GITHUB_DATA = "true"
// Set to your username in order to fetch blog data from Medium (otherwise, hardcoded values from Blogs.js are used)
MEDIUM_USERNAME = "YOU MEDIUM USERNAME HERE"
